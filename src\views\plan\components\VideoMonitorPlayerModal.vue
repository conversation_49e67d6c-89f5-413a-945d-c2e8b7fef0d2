<template>
  <div class="video-monitor-player-modal">
    <!-- 视频播放区域 -->
    <div class="video-player-wrapper">
      <div class="video-header">
        <div class="video-title">
          <h5>{{ videoInfo?.name || '视频监控' }}</h5>
          <a-tag v-if="videoInfo?.streamType" :color="getStreamTypeColor(videoInfo.streamType)" size="small">
            {{ getStreamTypeText(videoInfo.streamType) }}
          </a-tag>
        </div>
        <div class="video-controls" v-if="showControls">
          <a-button
              type="primary"
              @click="startVideo"
              :loading="starting"
              :disabled="isPlaying"
              size="small"
              title="开始播放"
          >
            <PlayCircleOutlined />
            {{ starting ? '启动中...' : '播放' }}
          </a-button>
          <a-button
              @click="stopVideo"
              :disabled="!isPlaying && !starting"
              size="small"
              title="停止播放"
          >
            <PauseCircleOutlined />
            停止
          </a-button>
          <a-button
              @click="refreshVideo"
              :disabled="starting"
              size="small"
              title="刷新视频"
          >
            <ReloadOutlined />
            刷新
          </a-button>
          <a-button
              @click="captureFrame"
              :disabled="!isPlaying"
              size="small"
              title="截图"
          >
            <CameraOutlined />
            截图
          </a-button>
        </div>
      </div>

      <div class="video-content">
        <!-- 加载状态 -->
        <div v-if="starting" class="video-loading">
          <a-spin size="large">
            <template #indicator>
              <LoadingOutlined style="font-size: 24px" spin />
            </template>
          </a-spin>
          <p>正在连接视频流...</p>
        </div>

        <!-- HLS视频播放器 -->
        <div v-else-if="hlsSupported" class="video-wrapper">
          <video
              ref="videoElement"
              class="video-player"
              controls
              autoplay
              muted
              :poster="videoPoster"
              @loadstart="onVideoLoadStart"
              @loadeddata="onVideoLoaded"
              @error="onVideoError"
              @play="onVideoPlay"
              @pause="onVideoPause"
              @ended="onVideoEnded"
              @waiting="onVideoWaiting"
              @canplay="onVideoCanPlay"
              @stalled="onVideoStalled"
          >
            您的浏览器不支持视频播放
          </video>

          <!-- 视频覆盖层 -->
          <div class="video-overlay" v-if="!isPlaying && !starting">
            <div class="play-button" @click="startVideo">
              <PlayCircleOutlined />
              <span class="play-text">点击播放</span>
            </div>
          </div>

          <!-- 启动中覆盖层 -->
          <div class="video-overlay starting-overlay" v-if="starting">
            <div class="starting-content">
              <LoadingOutlined class="loading-icon" />
              <span class="starting-text">正在启动视频流...</span>
            </div>
          </div>
        </div>

        <!-- WebSocket视频流（备用方案） -->
        <div v-else class="video-wrapper">
          <canvas
              ref="canvasElement"
              class="video-canvas"
              :width="canvasWidth"
              :height="canvasHeight"
          ></canvas>

          <!-- Canvas覆盖层 -->
          <div class="video-overlay" v-if="!isPlaying && !starting">
            <div class="play-button" @click="startVideo">
              <PlayCircleOutlined />
            </div>
          </div>
        </div>

        <!-- 视频信息栏 -->
        <div class="video-info-bar" v-if="showControls">
          <div class="status-indicators">
            <a-tag :color="isPlaying ? 'green' : 'red'" size="small">
              {{ isPlaying ? '播放中' : '已停止' }}
            </a-tag>
            <a-tag :color="wsConnected ? 'green' : 'red'" size="small">
              {{ wsConnected ? '已连接' : '未连接' }}
            </a-tag>
            <span class="duration">{{ playDuration }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息显示 -->
    <a-alert
        v-if="errorMessage"
        :message="errorMessage"
        type="error"
        closable
        @close="errorMessage = ''"
        style="margin-top: 8px"
        size="small"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  CameraOutlined,
  LoadingOutlined
} from '@ant-design/icons-vue';
import Hls from 'hls.js';
import { useMyWebSocket, onWebSocket, offWebSocket } from '/@/hooks/web/useWebSocket';
import { defHttp } from '/@/utils/http/axios';

interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  hlsUrl: string;
  streamType?: string;
}

interface Props {
  planId: string;
  videoInfo: VideoInfo;
  autoStart?: boolean;
  showControls?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: false,
  showControls: true
});

// 响应式数据
const videoElement = ref<HTMLVideoElement>();
const canvasElement = ref<HTMLCanvasElement>();
const isPlaying = ref(false);
const starting = ref(false);
const wsConnected = ref(false);
const errorMessage = ref('');
const playStartTime = ref<number>(0);
const playDuration = ref('00:00:00');

// 使用系统WebSocket
const wsResult = useMyWebSocket();

let hls: Hls | null = null;
let durationTimer: NodeJS.Timeout | null = null;
let reconnectTimer: NodeJS.Timeout | null = null;
let waitingTimeout: NodeJS.Timeout | null = null;
let bufferCheckInterval: NodeJS.Timeout | null = null;
let reconnectAttempts = 0;
const maxReconnectAttempts = 5;
let healthCheckTimer: NodeJS.Timeout | null = null;

// 计算属性
const hlsSupported = computed(() => Hls.isSupported() || (videoElement.value?.canPlayType('application/vnd.apple.mpegurl')));
const canvasWidth = computed(() => 320);
const canvasHeight = computed(() => 240);
const videoPoster = computed(() => '/images/video-placeholder.jpg');

// 监听props变化
watch(() => props.videoInfo, (newInfo) => {
  if (newInfo && isPlaying.value) {
    stopVideo();
  }
  if (newInfo && props.autoStart) {
    nextTick(() => {
      startVideo();
    });
  }
}, { deep: true });

// 组件挂载
onMounted(() => {
  initializePlayer();

  // 监听系统WebSocket消息
  onWebSocket(handleVideoMessage);

  if (props.autoStart && props.videoInfo) {
    startVideo();
  }
});

// 组件卸载
onUnmounted(() => {
  // 移除WebSocket监听器
  offWebSocket(handleVideoMessage);
  cleanup();
});

/**
 * 初始化播放器
 */
function initializePlayer() {
  if (!props.videoInfo) {
    errorMessage.value = '视频信息未提供';
    return;
  }

  // 系统WebSocket已经在useWebSocket中初始化，这里不需要额外初始化
  wsConnected.value = true; // 假设系统WebSocket已连接
}

/**
 * 处理系统WebSocket消息
 */
function handleVideoMessage(data: any) {
  try {
    if (data.msgType === 'video' && data.data) {
      const videoData = data.data;

      // 检查是否是当前流的消息
      if (videoData.streamId !== props.videoInfo.streamId) {
        return;
      }

      switch (videoData.type) {
        case 'video_started':
          isPlaying.value = true;
          starting.value = false;
          startDurationTimer();
          console.log('视频流启动成功');
          break;
        case 'video_exists':
          // 视频流已存在，直接设置为播放状态
          isPlaying.value = true;
          starting.value = false;
          startDurationTimer();
          console.log('视频流已存在并正在运行');
          break;
        case 'video_stopped':
          isPlaying.value = false;
          stopDurationTimer();
          console.log('视频流已停止');
          break;
        case 'video_error':
          errorMessage.value = videoData.message;
          starting.value = false;
          console.error('视频流错误: ' + videoData.message);
          break;
        case 'video_frame':
          // 处理视频帧数据（如果需要）
          break;
        case 'segments_updated':
          // 处理新片段通知，刷新播放列表
          console.log('检测到新视频片段:', videoData.message);
          if (hls && isPlaying.value) {
            try {
              // 刷新播放列表以加载新片段
              const currentSrc = hls.url;
              if (currentSrc) {
                const refreshUrl = currentSrc.includes('?')
                  ? currentSrc + '&update=' + Date.now()
                  : currentSrc + '?update=' + Date.now();
                console.log('刷新播放列表以加载新片段');
                hls.loadSource(refreshUrl);
              }
            } catch (error) {
              console.error('刷新播放列表失败:', error);
            }
          }
          break;
        case 'recording_progress':
          // 处理录制进度更新
          console.log('录制进度更新:', videoData.message);
          break;
      }
    }
  } catch (error) {
    console.error('处理视频WebSocket消息失败:', error);
  }
}

/**
 * 开始播放视频
 */
async function startVideo() {
  if (!props.videoInfo?.videoUrl && !props.videoInfo?.streamId) {
    errorMessage.value = '视频源地址或流ID未设置';
    console.error('视频源地址或流ID未设置');
    return;
  }

  if (isPlaying.value) {
    console.log('视频已在播放中');
    return;
  }

  starting.value = true;
  errorMessage.value = '';

  console.log('开始播放视频:', {
    videoUrl: props.videoInfo.videoUrl,
    streamId: props.videoInfo.streamId,
    hlsSupported: hlsSupported.value
  });

  try {
    // 首先停止任何现有的播放
    if (hls) {
      hls.destroy();
      hls = null;
    }

    if (hlsSupported.value) {
      await startHlsVideo();
    } else {
      await startWebSocketVideo();
    }
  } catch (error: any) {
    console.error('启动视频失败:', error);
    errorMessage.value = '启动视频失败: ' + (error?.message || error);
    starting.value = false;
    console.error('启动视频失败: ' + (error?.message || error));
  }
}

/**
 * 启动HLS视频播放
 */
async function startHlsVideo() {
  if (!videoElement.value) {
    throw new Error('视频元素未找到');
  }

  try {
    console.log('开始启动HLS视频播放:', {
      hlsUrl: props.videoInfo.hlsUrl,
      videoUrl: props.videoInfo.videoUrl,
      streamId: props.videoInfo.streamId
    });

    // 首先通过HTTP API启动视频流转换
    await startVideoStreamAPI();

    // 等待更长时间让后端开始转换，并检查流状态
    await waitForStreamReady();

    if (Hls.isSupported()) {
      console.log('使用HLS.js播放视频');
      hls = new Hls({
        debug: false, // 关闭调试模式减少日志
        enableWorker: false, // 禁用Worker以避免解析问题
        lowLatencyMode: false, // 关闭低延迟模式，适合长视频播放

        // 优化缓冲配置以支持长视频边播边加载
        maxBufferLength: 120, // 增加最大缓冲长度到120秒，支持更长的预缓冲
        maxMaxBufferLength: 300, // 最大缓冲长度300秒（5分钟）
        maxBufferSize: 100 * 1000 * 1000, // 增加缓冲大小到100MB
        maxBufferHole: 0.3, // 减少缓冲空洞容忍度，确保连续播放

        // 缓冲管理配置 - 针对长视频优化
        highBufferWatchdogPeriod: 3, // 增加高缓冲监控周期
        nudgeOffset: 0.1,
        nudgeMaxRetry: 15, // 增加nudge重试次数
        maxFragLookUpTolerance: 0.3,

        // 针对长视频的播放配置
        backBufferLength: 60, // 保留60秒的后向缓冲，支持回退播放

        // 片段加载配置 - 优化长视频加载
        startFragPrefetch: true, // 启用片段预取
        testBandwidth: true, // 启用带宽测试以优化加载
        abrEwmaFastLive: 2.0, // 调整自适应比特率参数
        abrEwmaSlowLive: 8.0,
        abrEwmaFastVoD: 2.0,
        abrEwmaSlowVoD: 8.0,

        // 超时和重试配置 - 针对长视频和网络波动优化
        manifestLoadingTimeOut: 30000, // 增加清单加载超时到30秒
        manifestLoadingMaxRetry: 8, // 增加重试次数
        manifestLoadingRetryDelay: 2000, // 增加重试延迟
        levelLoadingTimeOut: 30000, // 增加级别加载超时
        levelLoadingMaxRetry: 15, // 增加重试次数
        levelLoadingRetryDelay: 2000, // 增加重试延迟
        fragLoadingTimeOut: 60000, // 增加片段加载超时到60秒
        fragLoadingMaxRetry: 50, // 大幅增加片段重试次数，确保长视频不中断
        fragLoadingRetryDelay: 1000, // 减少重试延迟到1秒，加快恢复速度

        // 错误处理和恢复配置
        enableSoftwareAES: true, // 启用软件AES解密
        forceKeyFrameOnDiscontinuity: true, // 在不连续处强制关键帧

        // 自动恢复和加载配置 - 长视频专用
        autoStartLoad: true,
        startPosition: 0, // 从开始位置播放，支持完整视频
        capLevelToPlayerSize: false,

        // 针对长视频的特殊配置
        liveDurationInfinity: false, // 关闭无限时长，使用VOD模式
        liveBackBufferLength: 60, // 直播回退缓冲长度
        maxLiveSyncPlaybackRate: 1, // 最大同步播放速率

        // 启用更积极的缓冲策略
        progressive: true, // 启用渐进式加载
        enableDateRangeMetadataCues: false, // 关闭日期范围元数据提示以节省资源
        enableEmsgMetadataCues: false, // 关闭emsg元数据提示
        enableID3MetadataCues: false, // 关闭ID3元数据提示

        // 长视频播放优化配置
        xhrSetup: function(xhr, url) {
          // 设置更长的超时时间
          xhr.timeout = 60000; // 60秒超时
        }
      });

      // 构建HLS URL，添加时间戳避免缓存
      const timestamp = Date.now();
      const hlsUrl = props.videoInfo.hlsUrl || `/jeecgboot/video/hls/${props.videoInfo.streamId}/index.m3u8?t=${timestamp}`;
      console.log('加载HLS源:', hlsUrl);

      hls.loadSource(hlsUrl);
      hls.attachMedia(videoElement.value);

      // 添加更多事件监听
      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log('HLS清单解析完成，开始播放');
        videoElement.value?.play().then(() => {
          isPlaying.value = true;
          starting.value = false;
          startDurationTimer();
          clearReconnectTimer(); // 播放成功，清除重连定时器
          startHealthCheck(); // 开始健康检查
          startBufferCheck(); // 开始缓冲检查
          console.log('视频播放成功');
        }).catch(error => {
          console.error('视频播放失败:', error);
          errorMessage.value = '视频播放失败: ' + error.message;
          starting.value = false;
        });
      });

      hls.on(Hls.Events.ERROR, (event, data) => {
        console.error('HLS错误:', {
          type: data.type,
          details: data.details,
          fatal: data.fatal,
          error: data.error?.message || data.error,
          reason: data.reason,
          frag: data.frag ? {
            sn: data.frag.sn,
            url: data.frag.url,
            level: data.frag.level
          } : null
        });

        // 处理非致命错误
        if (!data.fatal) {
          console.warn('HLS非致命错误，尝试恢复:', data.details);

          // 针对特定的非致命错误进行处理
          switch (data.details) {
            case 'fragParsingError':
              console.log('片段解析错误，使用专门的处理函数');
              handleFragParsingError(data);
              break;
            case 'fragLoadError':
              console.log('片段加载错误，重新加载');
              if (hls) {
                hls.startLoad();
              }
              break;
            case 'manifestLoadError':
              console.log('清单加载错误，重新加载清单');
              if (hls) {
                setTimeout(() => {
                  hls.startLoad();
                }, 1000);
              }
              break;
            default:
              console.log('其他非致命错误，继续播放');
              break;
          }
          return;
        }

        // 处理致命错误
        console.error('HLS致命错误，开始恢复流程:', data.type);

        switch (data.type) {
          case Hls.ErrorTypes.NETWORK_ERROR:
            console.log('网络错误，尝试重新加载...');
            if (hls) {
              try {
                hls.startLoad();
                // 如果重新加载失败，尝试重连
                setTimeout(() => {
                  if (!isPlaying.value && starting.value) {
                    console.log('网络错误恢复失败，尝试重连');
                    attemptAutoReconnect();
                  }
                }, 5000);
              } catch (error) {
                console.error('网络错误恢复失败:', error);
                attemptAutoReconnect();
              }
            }
            break;

          case Hls.ErrorTypes.MEDIA_ERROR:
            console.log('媒体错误，尝试媒体恢复...');
            if (hls) {
              try {
                // 针对不同的媒体错误采用不同策略
                switch (data.details) {
                  case 'fragParsingError':
                    console.log('片段解析错误，尝试重新创建HLS实例');
                    // 对于解析错误，重新创建HLS实例可能更有效
                    recreateHlsInstance();
                    break;
                  case 'fragDecryptError':
                    console.log('片段解密错误，尝试媒体恢复');
                    hls.recoverMediaError();
                    break;
                  case 'keyLoadError':
                    console.log('密钥加载错误，尝试重新加载');
                    hls.startLoad();
                    break;
                  default:
                    console.log('通用媒体错误，尝试媒体恢复');
                    hls.recoverMediaError();
                    break;
                }

                // 如果媒体恢复失败，尝试重连
                setTimeout(() => {
                  if (!isPlaying.value && starting.value) {
                    console.log('媒体错误恢复失败，尝试重连');
                    attemptAutoReconnect();
                  }
                }, 3000);
              } catch (error) {
                console.error('媒体错误恢复失败:', error);
                attemptAutoReconnect();
              }
            }
            break;

          default:
            console.log('无法恢复的错误类型，尝试重连...');
            if (hls) {
              hls.destroy();
              hls = null;
            }
            isPlaying.value = false;
            starting.value = false;
            attemptAutoReconnect();
            break;
        }
      });

      // 添加更多事件监听以支持边播边加载
      hls.on(Hls.Events.FRAG_LOADED, (event, data) => {
        console.log('HLS片段加载完成:', {
          frag: data.frag.sn,
          duration: data.frag.duration,
          start: data.frag.start,
          end: data.frag.end
        });
      });

      hls.on(Hls.Events.LEVEL_SWITCHED, (_event, data) => {
        console.log('HLS级别切换:', data.level);
      });

      hls.on(Hls.Events.BUFFER_APPENDED, (event, data) => {
        console.log('HLS缓冲区已添加数据:', {
          type: data.type,
          timeRanges: data.timeRanges
        });

        // 检查缓冲区状态，确保持续加载
        if (videoElement.value) {
          const video = videoElement.value;
          const buffered = video.buffered;
          if (buffered.length > 0) {
            const bufferedEnd = buffered.end(buffered.length - 1);
            const currentTime = video.currentTime;
            const bufferedAhead = bufferedEnd - currentTime;

            console.log('缓冲状态:', {
              currentTime: currentTime.toFixed(2),
              bufferedEnd: bufferedEnd.toFixed(2),
              bufferedAhead: bufferedAhead.toFixed(2)
            });

            // 如果缓冲不足，主动触发加载
            if (bufferedAhead < 30 && hls) {
              console.log('缓冲不足，主动触发加载');
              hls.startLoad();
            }
          }
        }
      });

      hls.on(Hls.Events.BUFFER_EOS, () => {
        console.log('HLS缓冲区结束');
      });

      hls.on(Hls.Events.BUFFER_FLUSHED, (event, data) => {
        console.log('HLS缓冲区已刷新:', data);
      });

      // 监听片段加载开始
      hls.on(Hls.Events.FRAG_LOADING, (event, data) => {
        console.log('开始加载HLS片段:', {
          frag: data.frag.sn,
          url: data.frag.url,
          start: data.frag.start,
          duration: data.frag.duration
        });
      });

      // 监听片段加载进度
      hls.on(Hls.Events.FRAG_LOAD_PROGRESS, (event, data) => {
        console.log('HLS片段加载进度:', {
          frag: data.frag.sn,
          loaded: data.stats.loaded,
          total: data.stats.total,
          progress: data.stats.total > 0 ? (data.stats.loaded / data.stats.total * 100).toFixed(1) + '%' : '0%'
        });
      });

      // 监听片段加载错误
      hls.on(Hls.Events.FRAG_LOAD_ERROR, (event, data) => {
        console.error('HLS片段加载错误:', {
          frag: data.frag ? data.frag.sn : 'unknown',
          url: data.frag ? data.frag.url : 'unknown',
          response: data.response,
          error: data.error?.message || data.error
        });

        // 如果是404错误且流仍活跃，可能是片段还没生成，稍后重试
        if (data.response && data.response.code === 404 && isPlaying.value) {
          console.log('片段404错误，可能还在生成中，HLS会自动重试');
        }
      });

      // 监听清单加载完成
      hls.on(Hls.Events.LEVEL_LOADED, (event, data) => {
        console.log('HLS级别加载完成:', {
          level: data.level,
          details: data.details
        });
      });

      hls.on(Hls.Events.FRAG_PARSING_USERDATA, () => {
        // 片段正在解析，表示流正常
        if (!isPlaying.value && videoElement.value && !videoElement.value.paused) {
          isPlaying.value = true;
          starting.value = false;
        }
      });

      // 添加播放进度监听，确保持续加载
      const progressHandler = () => {
        if (videoElement.value && hls) {
          const video = videoElement.value;
          const buffered = video.buffered;

          if (buffered.length > 0) {
            const currentTime = video.currentTime;
            const bufferedEnd = buffered.end(buffered.length - 1);
            const bufferedAhead = bufferedEnd - currentTime;

            // 如果缓冲时间少于20秒，主动加载更多数据
            if (bufferedAhead < 20) {
              console.log('播放接近缓冲区末端，主动加载更多数据');
              hls.startLoad();
            }
          }
        }
      };

      // 每5秒检查一次缓冲状态
      const bufferCheckInterval = setInterval(progressHandler, 5000);

      // 在组件卸载时清理定时器
      onUnmounted(() => {
        clearInterval(bufferCheckInterval);
      });

    } else if (videoElement.value.canPlayType('application/vnd.apple.mpegurl')) {
      console.log('使用Safari原生HLS支持');
      // Safari原生支持
      const timestamp = Date.now();
      const hlsUrl = props.videoInfo.hlsUrl || `/jeecgboot/video/hls/${props.videoInfo.streamId}/index.m3u8?t=${timestamp}`;
      videoElement.value.src = hlsUrl;

      try {
        await videoElement.value.play();
        isPlaying.value = true;
        starting.value = false;
        startDurationTimer();
        clearReconnectTimer();
        startHealthCheck();
        console.log('视频播放成功');
      } catch (error: any) {
        console.error('Safari HLS播放失败:', error);
        errorMessage.value = 'Safari HLS播放失败: ' + (error?.message || error);
        starting.value = false;
        throw error;
      }
    } else {
      throw new Error('浏览器不支持HLS播放');
    }

  } catch (error: any) {
    console.error('HLS播放失败:', error);
    errorMessage.value = 'HLS播放失败: ' + (error?.message || error);
    starting.value = false;
    throw error;
  }
}

/**
 * 启动WebSocket视频播放
 */
async function startWebSocketVideo() {
  // 通过HTTP API启动视频流转换
  await startVideoStreamAPI();
}

/**
 * 停止视频播放
 */
async function stopVideo() {
  try {
    console.log('停止视频播放:', props.videoInfo.streamId);

    // 停止HLS播放
    if (hls) {
      console.log('销毁HLS实例');
      hls.destroy();
      hls = null;
    }

    // 停止视频元素
    if (videoElement.value) {
      console.log('停止视频元素');
      videoElement.value.pause();
      videoElement.value.src = '';
      videoElement.value.load(); // 重置视频元素
    }

    // 通过HTTP API停止视频流
    await stopVideoStreamAPI();

    isPlaying.value = false;
    starting.value = false;
    stopDurationTimer();
    stopHealthCheck(); // 停止健康检查
    stopBufferCheck(); // 停止缓冲检查
    clearReconnectTimer(); // 清除重连定时器
    errorMessage.value = '';

    console.log('视频播放已停止');

  } catch (error: any) {
    console.error('停止视频失败:', error);
    errorMessage.value = '停止视频失败: ' + (error?.message || error);
  }
}

/**
 * 刷新视频
 */
async function refreshVideo() {
  console.log('刷新视频...');

  try {
    // 停止当前播放
    await stopVideo();

    // 等待一段时间确保资源完全释放
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 重新启动播放
    await startVideo();

    console.log('视频已刷新');
  } catch (error: any) {
    console.error('刷新视频失败:', error);
    errorMessage.value = '刷新视频失败: ' + (error?.message || error);
    console.error('刷新视频失败');
  }
}

/**
 * 截图
 */
function captureFrame() {
  if (!videoElement.value || !isPlaying.value) return;

  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    canvas.width = videoElement.value.videoWidth;
    canvas.height = videoElement.value.videoHeight;

    ctx?.drawImage(videoElement.value, 0, 0);

    const dataUrl = canvas.toDataURL('image/png');

    // 下载截图
    const link = document.createElement('a');
    link.download = `screenshot_${Date.now()}.png`;
    link.href = dataUrl;
    link.click();

    console.log('截图成功');

  } catch (error) {
    console.error('截图失败:', error);
    console.error('截图失败');
  }
}

/**
 * 获取流类型颜色
 */
function getStreamTypeColor(type: string) {
  switch (type) {
    case 'playback': return 'blue';
    case 'preview': return 'green';
    default: return 'default';
  }
}

/**
 * 获取流类型文本
 */
function getStreamTypeText(type: string) {
  switch (type) {
    case 'playback': return '回放流';
    case 'preview': return '预览流';
    default: return '未知';
  }
}

/**
 * 视频事件处理
 */
function onVideoLoadStart() {
  console.log('视频开始加载');
}

function onVideoLoaded() {
  console.log('视频加载完成');
}

function onVideoError(event: Event) {
  console.error('视频播放错误:', event);
  errorMessage.value = '视频播放错误';
  isPlaying.value = false;
  starting.value = false;
  attemptAutoReconnect();
}

function onVideoPlay() {
  console.log('视频开始播放');
  isPlaying.value = true;
  starting.value = false;
  clearReconnectTimer();
}

function onVideoPause() {
  console.log('视频暂停');
  // 注意：不要在这里设置 isPlaying.value = false，因为可能是缓冲导致的暂停
}

function onVideoEnded() {
  console.log('视频播放结束');
  isPlaying.value = false;
  stopDurationTimer();
  stopHealthCheck();
}

function onVideoWaiting() {
  console.log('视频等待数据...', {
    isPlaying: isPlaying.value,
    starting: starting.value,
    hasHls: !!hls
  });

  // 视频在等待数据，可能是网络问题或缓冲不足
  // 如果HLS实例存在，尝试重新加载
  if (hls) {
    console.log('尝试重新加载HLS流以解决等待问题');
    try {
      hls.startLoad();
    } catch (error) {
      console.error('重新加载HLS流失败:', error);
    }
  }

  // 清除之前的超时
  if (waitingTimeout) {
    clearTimeout(waitingTimeout);
  }

  // 设置一个超时，如果长时间等待则尝试恢复
  waitingTimeout = setTimeout(() => {
    if (videoElement.value && videoElement.value.readyState < 3) {
      console.warn('视频长时间等待数据，尝试恢复播放');

      if (hls) {
        console.log('尝试HLS媒体错误恢复');
        try {
          hls.recoverMediaError();
        } catch (error) {
          console.error('HLS媒体错误恢复失败:', error);
          // 如果恢复失败，尝试重新启动
          attemptAutoReconnect();
        }
      } else if (videoElement.value) {
        // 非HLS模式，尝试重新播放
        console.log('尝试重新播放视频');
        const video = videoElement.value;
        video.load();
        video.play().catch(error => {
          console.error('重新播放失败:', error);
        });
      }
    }
  }, 5000); // 5秒后尝试恢复
}

function onVideoCanPlay() {
  console.log('视频可以播放', {
    isPlaying: isPlaying.value,
    starting: starting.value,
    paused: videoElement.value?.paused,
    readyState: videoElement.value?.readyState
  });

  // 清除等待超时，因为视频现在可以播放了
  clearWaitingTimeout();

  // 如果正在启动且视频未暂停，设置为播放状态
  if (starting.value && videoElement.value && !videoElement.value.paused) {
    isPlaying.value = true;
    starting.value = false;
    startDurationTimer();
    console.log('视频播放状态已更新');
  }

  // 如果视频处于等待状态但现在可以播放，尝试播放
  if (!isPlaying.value && starting.value && videoElement.value) {
    console.log('尝试播放可用的视频');
    videoElement.value.play().then(() => {
      isPlaying.value = true;
      starting.value = false;
      startDurationTimer();
      console.log('视频播放成功');
    }).catch(error => {
      console.error('播放视频失败:', error);
    });
  }
}

function onVideoStalled() {
  console.log('视频播放停滞', {
    isPlaying: isPlaying.value,
    currentTime: videoElement.value?.currentTime,
    buffered: videoElement.value?.buffered.length || 0,
    networkState: videoElement.value?.networkState,
    readyState: videoElement.value?.readyState
  });

  // 视频播放停滞，可能需要重新加载
  if (hls) {
    console.log('尝试重新加载HLS流以解决停滞问题');
    try {
      hls.startLoad();
    } catch (error) {
      console.error('重新加载HLS流失败:', error);
    }
  }

  // 如果停滞时间过长，尝试其他恢复方法
  setTimeout(() => {
    if (videoElement.value && videoElement.value.readyState < 3) {
      console.warn('视频仍然停滞，尝试更积极的恢复');
      if (hls) {
        try {
          hls.recoverMediaError();
        } catch (error) {
          console.error('HLS媒体错误恢复失败:', error);
          attemptAutoReconnect();
        }
      }
    }
  }, 3000); // 3秒后检查
}

/**
 * 开始计时器
 */
function startDurationTimer() {
  playStartTime.value = Date.now();
  durationTimer = setInterval(() => {
    const elapsed = Date.now() - playStartTime.value;
    playDuration.value = formatDuration(elapsed);
  }, 1000);
}

/**
 * 停止计时器
 */
function stopDurationTimer() {
  if (durationTimer) {
    clearInterval(durationTimer);
    durationTimer = null;
  }
  playDuration.value = '00:00:00';
}

/**
 * 格式化时长
 */
function formatDuration(ms: number): string {
  const seconds = Math.floor(ms / 1000);
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
}

/**
 * 尝试自动重连
 */
function attemptAutoReconnect() {
  if (reconnectAttempts >= maxReconnectAttempts) {
    console.log('已达到最大重连次数，停止重连');
    errorMessage.value = '视频连接失败，已达到最大重连次数';
    return;
  }

  reconnectAttempts++;
  const delay = Math.min(1000 * reconnectAttempts, 5000); // 递增延迟，最大5秒

  console.log(`第 ${reconnectAttempts} 次自动重连，${delay}ms 后开始...`);

  reconnectTimer = setTimeout(async () => {
    try {
      console.log('开始自动重连...');
      await startVideo();
      reconnectAttempts = 0; // 重连成功，重置计数器
      console.log('视频重连成功');
    } catch (error: any) {
      console.error('自动重连失败:', error);
      // 继续尝试重连
      attemptAutoReconnect();
    }
  }, delay);
}

/**
 * 清除重连定时器
 */
function clearReconnectTimer() {
  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }
  reconnectAttempts = 0;
}

/**
 * 开始健康检查
 */
function startHealthCheck() {
  stopHealthCheck(); // 先停止之前的检查

  healthCheckTimer = setInterval(() => {
    if (!isPlaying.value || !videoElement.value) {
      return;
    }

    // 检查视频是否真正在播放
    const video = videoElement.value;
    const currentTime = video.currentTime;

    // 等待一段时间后再次检查
    setTimeout(() => {
      if (isPlaying.value && video.currentTime === currentTime && !video.paused) {
        console.warn('视频可能已停止播放，尝试恢复...');
        // 视频时间没有变化，可能已停止
        if (hls) {
          hls.startLoad();
        } else {
          // 如果不是HLS，尝试重新播放
          video.play().catch(error => {
            console.error('重新播放失败:', error);
            attemptAutoReconnect();
          });
        }
      }
    }, 3000); // 3秒后检查
  }, 10000); // 每10秒检查一次
}

/**
 * 停止健康检查
 */
function stopHealthCheck() {
  if (healthCheckTimer) {
    clearInterval(healthCheckTimer);
    healthCheckTimer = null;
  }
}

/**
 * 等待流准备就绪
 */
async function waitForStreamReady() {
  try {
    console.log('等待流准备就绪:', props.videoInfo.streamId);

    // 首先使用后端的等待接口
    const response = await defHttp.get({
      url: `/video/wait/${props.videoInfo.streamId}`,
      params: {
        maxWaitSeconds: 15 // 等待15秒
      },
      timeout: 20000 // HTTP超时20秒
    });

    console.log('等待流准备就绪响应:', response);

    if (response && response.ready) {
      console.log('流已准备就绪');
      return true;
    } else {
      console.warn('流未准备就绪，尝试备用检查方式:', response?.message || '未知原因');
      // 继续使用备用检查方式
    }
  } catch (error) {
    console.error('后端等待接口失败，使用备用检查方式:', error);
    // 继续使用备用检查方式
  }

  // 备用检查方式：轮询流状态
  return await waitForStreamReadyPolling();
}

/**
 * 轮询方式等待流准备就绪（备用方案）
 */
async function waitForStreamReadyPolling() {
  const maxRetries = 10; // 最大重试次数
  const retryDelay = 1000; // 重试间隔（毫秒）

  for (let i = 0; i < maxRetries; i++) {
    try {
      console.log(`轮询检查流状态，第 ${i + 1}/${maxRetries} 次尝试...`);

      // 方法1: 检查后端流状态
      try {
        const statusResponse = await defHttp.get({
          url: `/video/status/${props.videoInfo.streamId}`,
          timeout: 3000
        });

        if (statusResponse && statusResponse.isActive && statusResponse.hasFileInfo) {
          console.log('流已准备就绪（后端状态检查）');
          return true;
        }
      } catch (statusError) {
        console.debug('后端状态检查失败:', statusError);
      }

      // 方法2: 检查HLS播放列表
      try {
        const checkUrl = props.videoInfo.hlsUrl || `/jeecgboot/video/hls/${props.videoInfo.streamId}/index.m3u8`;
        const response = await fetch(checkUrl, {
          method: 'HEAD',
          cache: 'no-cache'
        });

        if (response.ok) {
          console.log('流已准备就绪（HLS检查）');
          return true;
        }

        console.log(`HLS检查失败，状态码: ${response.status}`);
      } catch (hlsError) {
        console.debug('HLS检查失败:', hlsError);
      }

    } catch (error) {
      console.log(`检查流状态失败: ${error}`);
    }

    // 等待后重试
    if (i < maxRetries - 1) {
      await new Promise(resolve => setTimeout(resolve, retryDelay));
    }
  }

  console.warn('流准备检查超时，继续尝试播放...');
  return false;
}

/**
 * 通过HTTP API启动视频流
 */
async function startVideoStreamAPI() {
  try {
    // 参数验证
    if (!props.videoInfo) {
      throw new Error('视频信息未提供');
    }

    if (!props.videoInfo.streamId || props.videoInfo.streamId.trim() === '') {
      throw new Error('流ID未设置');
    }

    if (!props.videoInfo.videoUrl || props.videoInfo.videoUrl.trim() === '') {
      throw new Error('视频URL未设置');
    }

    console.log('启动视频流API调用:', {
      streamId: props.videoInfo.streamId,
      rtspUrl: props.videoInfo.videoUrl,
      videoInfo: props.videoInfo
    });

    const response = await defHttp.post({
      url: '/video/start',
      data: {
        streamId: props.videoInfo.streamId,
        rtspUrl: props.videoInfo.videoUrl,
        userId: null // 后端会自动获取当前用户ID
      },
      timeout: 30000 // 30秒超时
    });

    console.log('启动视频流API响应:', response);

    // 验证响应
    if (!response || (response.success === false && response.code !== 200)) {
      throw new Error(response?.message || '启动视频流失败');
    }

    return response;
  } catch (error) {
    console.error('启动视频流API调用失败:', error);

    // 提供更详细的错误信息
    let errorMessage = '启动视频流失败';
    if (error.response) {
      errorMessage += ': ' + (error.response.data?.message || error.response.statusText || '服务器错误');
    } else if (error.message) {
      errorMessage += ': ' + error.message;
    }

    throw new Error(errorMessage);
  }
}

/**
 * 通过HTTP API停止视频流
 */
async function stopVideoStreamAPI() {
  try {
    console.log('停止视频流API调用:', {
      streamId: props.videoInfo.streamId
    });

    const response = await defHttp.post({
      url: '/video/stop',
      data: {
        streamId: props.videoInfo.streamId,
        userId: null // 后端会自动获取当前用户ID
      }
    });

    console.log('停止视频流API响应:', response);
    return response;
  } catch (error) {
    console.error('停止视频流API调用失败:', error);
  }
}

/**
 * 清理等待超时
 */
function clearWaitingTimeout() {
  if (waitingTimeout) {
    clearTimeout(waitingTimeout);
    waitingTimeout = null;
  }
}

/**
 * 处理片段解析错误
 */
function handleFragParsingError(data: any) {
  console.log('处理片段解析错误:', {
    frag: data.frag ? data.frag.sn : 'unknown',
    reason: data.reason,
    error: data.error?.message
  });

  // 尝试多种恢复策略
  if (hls) {
    try {
      // 策略1: 尝试跳过当前片段
      if (data.frag && data.frag.start !== undefined) {
        console.log('尝试跳过有问题的片段');
        const nextPosition = data.frag.start + (data.frag.duration || 2);
        hls.startLoad(nextPosition);
        return;
      }

      // 策略2: 重新加载当前位置
      console.log('重新加载当前位置');
      hls.startLoad();

    } catch (error) {
      console.error('片段解析错误处理失败:', error);
      // 策略3: 重新创建HLS实例
      recreateHlsInstance();
    }
  }
}

/**
 * 重新创建HLS实例
 */
async function recreateHlsInstance() {
  console.log('重新创建HLS实例以解决解析错误');

  try {
    // 记录当前播放位置
    const currentTime = videoElement.value?.currentTime || 0;

    // 销毁现有实例
    if (hls) {
      hls.destroy();
      hls = null;
    }

    // 等待一段时间让资源释放
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 重新启动HLS播放
    if (videoElement.value) {
      console.log('重新启动HLS播放，从位置:', currentTime);

      // 重新创建HLS实例，使用更保守的配置
      await startHlsVideoWithFallback(currentTime);
    }
  } catch (error) {
    console.error('重新创建HLS实例失败:', error);
    // 如果重新创建失败，尝试完全重连
    attemptAutoReconnect();
  }
}

/**
 * 使用备用配置启动HLS视频
 */
async function startHlsVideoWithFallback(startTime: number = 0) {
  if (!videoElement.value) {
    throw new Error('视频元素未找到');
  }

  console.log('使用备用配置启动HLS视频');

  // 使用更保守的HLS配置
  hls = new Hls({
    debug: false,
    enableWorker: false, // 禁用Worker
    lowLatencyMode: false,

    // 更保守的缓冲配置
    maxBufferLength: 30,
    maxMaxBufferLength: 60,
    maxBufferSize: 30 * 1000 * 1000,
    maxBufferHole: 0.3,

    // 减少重试次数，避免无限循环
    manifestLoadingMaxRetry: 3,
    levelLoadingMaxRetry: 5,
    fragLoadingMaxRetry: 8,

    // 更长的超时时间
    fragLoadingTimeOut: 60000,
    manifestLoadingTimeOut: 30000,
    levelLoadingTimeOut: 30000,

    // 启用软件解密
    enableSoftwareAES: true,
    autoStartLoad: true,
    startPosition: startTime
  });

  const timestamp = Date.now();
  const hlsUrl = props.videoInfo.hlsUrl || `/jeecgboot/video/hls/${props.videoInfo.streamId}/index.m3u8?t=${timestamp}`;

  hls.loadSource(hlsUrl);
  hls.attachMedia(videoElement.value);

  // 简化的事件处理
  hls.on(Hls.Events.MANIFEST_PARSED, () => {
    console.log('备用HLS清单解析完成');
    videoElement.value?.play().then(() => {
      isPlaying.value = true;
      starting.value = false;
      startDurationTimer();
      startHealthCheck();
      startBufferCheck();
      console.log('备用HLS播放成功');
    }).catch(error => {
      console.error('备用HLS播放失败:', error);
      attemptAutoReconnect();
    });
  });

  // 简化的错误处理
  hls.on(Hls.Events.ERROR, (event, data) => {
    console.error('备用HLS错误:', data.details);
    if (data.fatal) {
      console.log('备用HLS致命错误，尝试重连');
      attemptAutoReconnect();
    }
  });
}

/**
 * 启动缓冲检查
 */
function startBufferCheck() {
  if (bufferCheckInterval) {
    clearInterval(bufferCheckInterval);
  }

  bufferCheckInterval = setInterval(() => {
    if (videoElement.value && hls && isPlaying.value) {
      const video = videoElement.value;
      const buffered = video.buffered;

      if (buffered.length > 0) {
        const currentTime = video.currentTime;
        const bufferedEnd = buffered.end(buffered.length - 1);
        const bufferedAhead = bufferedEnd - currentTime;
        const duration = video.duration;

        console.log('缓冲检查:', {
          currentTime: currentTime.toFixed(2),
          bufferedEnd: bufferedEnd.toFixed(2),
          bufferedAhead: bufferedAhead.toFixed(2),
          duration: duration ? duration.toFixed(2) : 'unknown',
          progress: duration ? ((currentTime / duration) * 100).toFixed(1) + '%' : 'unknown'
        });

        // 长视频播放优化：更积极的缓冲策略
        const isLongVideo = !duration || duration > 300; // 超过5分钟认为是长视频
        const bufferThreshold = isLongVideo ? 60 : 20; // 长视频使用更大的缓冲阈值

        // 如果缓冲时间少于阈值且还没播放完，主动加载更多数据
        if (bufferedAhead < bufferThreshold && (!duration || currentTime < duration - 10)) {
          console.log('缓冲不足，主动触发加载更多数据 - 阈值:', bufferThreshold, '当前缓冲:', bufferedAhead.toFixed(2));
          try {
            hls.startLoad();
          } catch (error) {
            console.error('主动加载失败:', error);
          }
        }

        // 对于长视频，更早开始加载结束部分
        const endLoadThreshold = isLongVideo ? 60 : 30;
        if (duration && currentTime > duration - endLoadThreshold && bufferedEnd < duration - 5) {
          console.log('接近播放结束，确保加载完整视频 - 剩余时间:', (duration - currentTime).toFixed(2));
          try {
            hls.startLoad();
          } catch (error) {
            console.error('结束段加载失败:', error);
          }
        }

        // 检查是否需要刷新播放列表（针对长视频流式更新）
        if (isLongVideo && bufferedAhead < 30) {
          console.log('长视频缓冲不足，尝试刷新播放列表');
          try {
            // 重新加载播放列表以获取最新的片段
            const currentSrc = hls.url;
            if (currentSrc) {
              const refreshUrl = currentSrc.includes('?')
                ? currentSrc + '&refresh=' + Date.now()
                : currentSrc + '?refresh=' + Date.now();
              hls.loadSource(refreshUrl);
            }
          } catch (error) {
            console.error('刷新播放列表失败:', error);
          }
        }
      }
    }
  }, 3000); // 每3秒检查一次
}

/**
 * 停止缓冲检查
 */
function stopBufferCheck() {
  if (bufferCheckInterval) {
    clearInterval(bufferCheckInterval);
    bufferCheckInterval = null;
  }
}

/**
 * 清理资源
 */
function cleanup() {
  console.log('VideoMonitorPlayerModal 开始清理资源:', props.videoInfo.streamId);

  // 清除所有定时器
  clearReconnectTimer(); // 清除重连定时器
  stopHealthCheck(); // 停止健康检查
  stopDurationTimer(); // 停止播放时长计时器
  clearWaitingTimeout(); // 清除等待超时
  stopBufferCheck(); // 停止缓冲检查

  // 停止视频播放和清理HLS资源
  stopVideo();

  // 强制清理HLS实例
  if (hls) {
    console.log('强制销毁HLS实例');
    try {
      hls.destroy();
    } catch (error) {
      console.error('销毁HLS实例时出错:', error);
    }
    hls = null;
  }

  // 清理视频元素
  if (videoElement.value) {
    console.log('清理视频元素');
    try {
      const video = videoElement.value;
      video.pause();
      video.src = '';
      video.load();
      // 事件监听器会在组件卸载时自动清理，这里不需要手动移除
    } catch (error) {
      console.error('清理视频元素时出错:', error);
    }
  }

  // 重置状态
  isPlaying.value = false;
  starting.value = false;
  wsConnected.value = false;
  errorMessage.value = '';
  playStartTime.value = 0;
  playDuration.value = '00:00:00';
  reconnectAttempts = 0;

  console.log('VideoMonitorPlayerModal 资源清理完成:', props.videoInfo.streamId);
}

// 暴露方法给父组件
defineExpose({
  startVideo,
  stopVideo,
  refreshVideo,
  captureFrame,
  cleanup,
  isPlaying: () => isPlaying.value
});
</script>

<style scoped>
.video-monitor-player-modal {
  width: 100%;
  height: 100%;
}

.video-player-wrapper {
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  flex-shrink: 0;
}

.video-title {
  display: flex;
  align-items: center;
  gap: 6px;
}

.video-title h5 {
  margin: 0;
  font-size: 12px;
  font-weight: 500;
}

.video-controls {
  display: flex;
  gap: 4px;
}

.video-content {
  position: relative;
  background: #000;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 150px;
  color: white;
  gap: 12px;
}

.video-wrapper {
  position: relative;
  width: 100%;
  flex: 1;
  background: #000;
  min-height: 120px;
}

.video-player {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.video-canvas {
  display: block;
  margin: 0 auto;
  background: #000;
  width: 100%;
  height: 100%;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.video-overlay:hover {
  background: rgba(0, 0, 0, 0.5);
}

.play-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-size: 48px;
  color: white;
  opacity: 0.9;
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 20px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(5px);
}

.play-button:hover {
  opacity: 1;
  transform: scale(1.1);
  background: rgba(0, 0, 0, 0.5);
}

.play-text {
  font-size: 14px;
  font-weight: 500;
  margin-top: 4px;
}

.starting-overlay {
  background: rgba(0, 0, 0, 0.7);
}

.starting-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: white;
}

.loading-icon {
  font-size: 32px;
  animation: spin 1s linear infinite;
}

.starting-text {
  font-size: 14px;
  font-weight: 500;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.video-info-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 8px;
  background: #f5f5f5;
  border-top: 1px solid #d9d9d9;
  flex-shrink: 0;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 6px;
}

.duration {
  font-family: 'Courier New', monospace;
  font-size: 10px;
  color: #666;
}
</style>
