<template>
  <div class="prison-visualization-fullscreen">
    <PrisonVisualization />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';
import PrisonVisualization from './PrisonVisualization.vue';

// 进入全屏模式
const enterFullscreen = () => {
  const element = document.documentElement;
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if ((element as any).webkitRequestFullscreen) {
    (element as any).webkitRequestFullscreen();
  } else if ((element as any).msRequestFullscreen) {
    (element as any).msRequestFullscreen();
  }
};

// 退出全屏模式
const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if ((document as any).webkitExitFullscreen) {
    (document as any).webkitExitFullscreen();
  } else if ((document as any).msExitFullscreen) {
    (document as any).msExitFullscreen();
  }
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  // ESC键退出全屏
  if (event.key === 'Escape') {
    exitFullscreen();
  }
  // F11键切换全屏
  if (event.key === 'F11') {
    event.preventDefault();
    if (document.fullscreenElement) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  }
};

onMounted(() => {
  // 自动进入全屏
  setTimeout(() => {
    enterFullscreen();
  }, 500);
  
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown);
  
  // 退出全屏
  exitFullscreen();
});
</script>

<style lang="less" scoped>
.prison-visualization-fullscreen {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9999;
  background: #000;

  :deep(.prison-visualization) {
    width: 1920px;
    height: 1080px;
    transform-origin: top left;

    // 自动缩放以适应不同屏幕尺寸
    @media (max-width: 1920px) {
      transform: scale(calc(100vw / 1920));
    }

    @media (max-height: 1080px) {
      transform: scale(calc(100vh / 1080));
    }

    @media (max-width: 1920px) and (max-height: 1080px) {
      transform: scale(min(calc(100vw / 1920), calc(100vh / 1080)));
    }
  }
}
</style>
