<template>
  <div class="patrol-dashboard-index">
    <div class="page-header">
      <h1>分监区可视化数据大屏</h1>
      <p>选择查看模式</p>
    </div>
    
    <div class="view-options">
      <a-card class="option-card" @click="goToDashboard">
        <template #cover>
          <div class="card-cover dashboard-preview">
            <Icon icon="ant-design:dashboard-outlined" size="48" />
          </div>
        </template>
        <a-card-meta title="普通模式" description="在系统布局中查看大屏" />
      </a-card>
      
      <a-card class="option-card" @click="goToFullscreen">
        <template #cover>
          <div class="card-cover fullscreen-preview">
            <Icon icon="ant-design:fullscreen-outlined" size="48" />
          </div>
        </template>
        <a-card-meta title="全屏模式" description="全屏显示大屏，适合投影展示" />
      </a-card>

      <a-card class="option-card" @click="goToVisualization">
        <template #cover>
          <div class="card-cover visualization-preview">
            <Icon icon="ant-design:eye-outlined" size="48" />
          </div>
        </template>
        <a-card-meta title="监区可视化大屏" description="多分监区人员和巡更状态展示" />
      </a-card>

      <a-card class="option-card" @click="goToVisualizationFullscreen">
        <template #cover>
          <div class="card-cover visualization-fullscreen-preview">
            <Icon icon="ant-design:desktop-outlined" size="48" />
          </div>
        </template>
        <a-card-meta title="监区可视化全屏" description="监区可视化大屏全屏模式" />
      </a-card>

      <a-card class="option-card" @click="goToCommandCenter">
        <template #cover>
          <div class="card-cover command-center-preview">
            <Icon icon="ant-design:control-outlined" size="48" />
          </div>
        </template>
        <a-card-meta title="指挥中心大屏" description="多监区指挥中心可视化数据大屏" />
      </a-card>

      <a-card class="option-card" @click="goToCommandCenterFullscreen">
        <template #cover>
          <div class="card-cover command-center-fullscreen-preview">
            <Icon icon="ant-design:monitor-outlined" size="48" />
          </div>
        </template>
        <a-card-meta title="指挥中心全屏" description="指挥中心大屏全屏模式，1920×1080px" />
      </a-card>
    </div>
    
    <div class="features">
      <h2>功能特点</h2>
      <div class="feature-list">
        <div class="feature-item">
          <Icon icon="ant-design:team-outlined" />
          <span>实时显示今日巡更人员信息（正方形照片）</span>
        </div>
        <div class="feature-item">
          <Icon icon="ant-design:clock-circle-outlined" />
          <span>动态展示巡更计划进度（12个巡更点）</span>
        </div>
        <div class="feature-item">
          <Icon icon="ant-design:environment-outlined" />
          <span>巡更点状态可视化（绿色-正常，黄色-待巡，红色-漏检）</span>
        </div>
        <div class="feature-item">
          <Icon icon="ant-design:eye-outlined" />
          <span>响应式科技感界面设计</span>
        </div>
        <div class="feature-item">
          <Icon icon="ant-design:line-outlined" />
          <span>智能连接线系统，真正连接巡更点</span>
        </div>
        <div class="feature-item">
          <Icon icon="ant-design:compress-outlined" />
          <span>紧凑列表设计，无滚动条，所有信息一屏显示</span>
        </div>
        <div class="feature-item">
          <Icon icon="ant-design:fullscreen-outlined" />
          <span>一键全屏显示，隐藏菜单栏</span>
        </div>
        <div class="feature-item">
          <Icon icon="ant-design:mobile-outlined" />
          <span>响应式设计，适配不同屏幕尺寸</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import { Icon } from '/@/components/Icon';

const router = useRouter();

const goToDashboard = () => {
  router.push('/patrolDashboard/patrolDashboard');
};

const goToFullscreen = () => {
  router.push('/patrolDashboard/patrolDashboardfullscreen');
};

const goToVisualization = () => {
  router.push('/patrol/visualization');
};

const goToVisualizationFullscreen = () => {
  router.push('/patrol/visualization-fullscreen');
};

const goToCommandCenter = () => {
  router.push('/patrol/command-center');
};

const goToCommandCenterFullscreen = () => {
  router.push('/patrol/command-center-fullscreen');
};
</script>

<style lang="less" scoped>
.patrol-dashboard-index {
  padding: 40px;
  max-width: 1200px;
  margin: 0 auto;
  
  .page-header {
    text-align: center;
    margin-bottom: 60px;
    
    h1 {
      font-size: 36px;
      color: #1890ff;
      margin-bottom: 16px;
    }
    
    p {
      font-size: 16px;
      color: #666;
    }
  }
  
  .view-options {
    display: flex;
    gap: 40px;
    justify-content: center;
    margin-bottom: 80px;
    
    .option-card {
      width: 300px;
      cursor: pointer;
      transition: all 0.3s ease;
      
      &:hover {
        transform: translateY(-8px);
        box-shadow: 0 8px 30px rgba(24, 144, 255, 0.2);
      }
      
      .card-cover {
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 48px;
        
        &.dashboard-preview {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        &.fullscreen-preview {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }

        &.visualization-preview {
          background: linear-gradient(135deg, #722ed1 0%, #eb2f96 100%);
        }

        &.visualization-fullscreen-preview {
          background: linear-gradient(135deg, #13c2c2 0%, #52c41a 100%);
        }

        &.command-center-preview {
          background: linear-gradient(135deg, #fa8c16 0%, #faad14 100%);
        }

        &.command-center-fullscreen-preview {
          background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
        }
      }
    }
  }
  
  .features {
    .feature-list {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 20px;
      margin-top: 30px;
      
      .feature-item {
        display: flex;
        align-items: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #1890ff;
        
        .anticon {
          font-size: 24px;
          color: #1890ff;
          margin-right: 16px;
        }
        
        span {
          font-size: 16px;
          color: #333;
        }
      }
    }
  }
}
</style>
