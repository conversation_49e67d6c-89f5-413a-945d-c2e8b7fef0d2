<template>
  <j-modal :title="title" :width="width" v-model:open="open" :footer="null" @cancel="handleCancel" class="tech-modal">
    <div class="tech-container mx-auto px-4 py-6 relative">
      <!-- 背景科技网格 -->
      <div class="tech-grid-bg"></div>

      <!-- 计划信息卡片 -->
      <div class="tech-card bg-gradient-to-br from-white/98 to-blue-50/95 backdrop-blur-xl rounded-2xl shadow-tech p-6 mb-6 transform transition-all duration-500 hover:shadow-tech-hover relative border border-blue-200/40">
        <!-- 科技感装饰元素 -->
        <div class="absolute top-0 right-0 w-40 h-1 bg-gradient-to-r from-transparent via-blue-400 to-cyan-500 animate-pulse-slow"></div>
        <div class="absolute bottom-0 left-0 w-40 h-1 bg-gradient-to-r from-cyan-500 via-blue-400 to-transparent animate-pulse-slow"></div>

        <div class="flex flex-col md:flex-row md:items-center justify-between mb-6">
          <div>
            <div class="flex items-center">
              <h2 class="text-[clamp(1.25rem,3vw,1.75rem)] font-bold text-gray-800 mr-4 text-shadow-tech">{{ info.name }}</h2>
              <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border tech-status-badge" :class="getStatusBadgeClassLight(info.status)">
                <span class="w-2 h-2 rounded-full mr-2 animate-pulse-glow" :class="getStatusDotClassLight(info.status)"></span>{{ info.statusName }}
              </span>
            </div>
            <p class="text-blue-600 mt-2 flex items-center">
              <Icon icon="mdi:map-marker-radius" size="16" class="mr-2 text-blue-500 animate-pulse"></Icon>
              {{ info.lineName}}
            </p>
          </div>
          <div class="mt-4 md:mt-0 flex flex-wrap gap-3">
            <div class="tech-time-card bg-gradient-to-r from-blue-50/90 to-cyan-50/90 backdrop-blur-sm rounded-xl px-4 py-3 flex items-center border border-blue-200/50">
              <div class="flex flex-col">
                <span class="text-xs text-blue-600">开始时间</span>
                <span class="font-medium text-gray-800">{{ info.startTime }}</span>
              </div>
              <div class="mx-3 h-8 w-px bg-gradient-to-b from-blue-400 to-cyan-500"></div>
              <div class="flex flex-col">
                <span class="text-xs text-blue-600">结束时间</span>
                <span class="font-medium text-gray-800">{{ info.endTime == null ? "未结束" : info.endTime }}</span>
              </div>
            </div>
            <!-- 新增：巡更用时 -->
            <div class="tech-time-card bg-gradient-to-r from-purple-50/90 to-indigo-50/90 backdrop-blur-sm rounded-xl px-4 py-3 flex items-center border border-purple-200/50">
              <div class="flex flex-col">
                <span class="text-xs text-purple-600">巡更用时</span>
                <span class="font-medium text-gray-800">{{ info.patrolDuration || '计算中...' }}</span>
              </div>
            </div>

          </div>
        </div>

        <!-- 新增：巡检模式和创建信息 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div class="tech-info-card bg-gradient-to-br from-blue-50/80 to-cyan-50/80 backdrop-blur-sm rounded-xl p-4 flex items-center border border-blue-200/40 hover:border-blue-300/60 transition-all duration-300">
            <div class="w-10 h-10 rounded-full bg-gradient-to-br from-blue-100 to-cyan-100 flex items-center justify-center mr-3 border border-blue-300/50">
              <Icon icon="mdi:weather-sunny" class="text-blue-600"></Icon>
            </div>
            <div>
              <p class="text-xs text-blue-600">巡更模式</p>
              <p class="font-medium text-gray-800">{{ info.modeName }}</p>
            </div>
          </div>
          <div class="tech-info-card bg-gradient-to-br from-emerald-50/80 to-green-50/80 backdrop-blur-sm rounded-xl p-4 flex items-center border border-emerald-200/40 hover:border-emerald-300/60 transition-all duration-300">
            <div class="w-10 h-10 rounded-full bg-gradient-to-br from-emerald-100 to-green-100 flex items-center justify-center mr-3 border border-emerald-300/50">
              <Icon icon="mdi:account" class="text-emerald-600"></Icon>
            </div>
            <div>
              <p class="text-xs text-emerald-600">创建人</p>
              <p class="font-medium text-gray-800">{{ info.createUserName}}</p>
            </div>
          </div>
          <div class="tech-info-card bg-gradient-to-br from-orange-50/80 to-amber-50/80 backdrop-blur-sm rounded-xl p-4 border border-orange-200/40 hover:border-orange-300/60 transition-all duration-300">
            <p class="text-xs text-orange-600 mb-3">巡更人员</p>
            <div v-if="info.patrolUserName" class="flex items-center">
              <div class="mr-3">
                <div class="relative">
                  <img
                      v-if="info.patrolUserImage"
                      :src="getFileAccessHttpUrl(info.patrolUserImage)"
                      class="w-12 h-12 rounded-full object-cover border-2 border-orange-300/60 shadow-lg tech-avatar"
                      :alt="info.patrolUserName"
                  />
                  <div
                      v-else
                      class="w-12 h-12 rounded-full bg-gradient-to-br from-orange-400 to-amber-500 flex items-center justify-center text-white text-sm font-medium shadow-lg border-2 border-orange-300/60 tech-avatar"
                  >
                    {{info.patrolUserName ? info.patrolUserName.substring(0, 1) : '?' }}
                  </div>
                  <!-- 在线状态指示器 -->
                  <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-400 rounded-full border-2 border-white animate-pulse"></div>
                </div>
              </div>
              <div>
                <p class="font-medium text-gray-800">{{ info.patrolUserName }}</p>
                <p class="text-xs text-orange-600">在线</p>
              </div>
            </div>
            <div v-else class="flex items-center">
              <div class="w-12 h-12 rounded-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center mr-3 border border-gray-300/50">
                <Icon icon="mdi:account-off" class="text-gray-500"></Icon>
              </div>
              <div>
                <span class="text-sm text-gray-600">未分配巡更人员</span>
                <p class="text-xs text-gray-500">待分配</p>
              </div>
            </div>
          </div>

          <div class="tech-info-card bg-gradient-to-br from-violet-50/80 to-purple-50/80 backdrop-blur-sm rounded-xl p-4 flex items-center border border-violet-200/40 hover:border-violet-300/60 transition-all duration-300">
            <div class="w-10 h-10 rounded-full bg-gradient-to-br from-violet-100 to-purple-100 flex items-center justify-center mr-3 border border-violet-300/50">
              <Icon icon="mdi:clock" class="text-violet-600"></Icon>
            </div>
            <div>
              <p class="text-xs text-violet-600">创建时间</p>
              <p class="font-medium text-gray-800">{{ info.createTime }}</p>
            </div>
          </div>
        </div>

        <!-- 进度条 -->
        <div class="mb-6 tech-progress-container">
          <div class="flex justify-between items-center mb-4">
            <span class="text-sm font-medium text-gray-800 flex items-center">
              <Icon icon="mdi:progress-check" class="mr-2 text-blue-600"></Icon>
              巡更进度
            </span>
            <span class="text-sm text-blue-600 font-medium bg-gradient-to-r from-blue-100 to-cyan-100 px-3 py-1 rounded-full border border-blue-300/50">{{ info.progress }} %</span>
          </div>
          <div class="relative">
            <div class="w-full bg-gray-200/80 rounded-full h-3 overflow-hidden border border-blue-200/40">
              <div
                  class="h-full bg-gradient-to-r from-blue-500 via-cyan-500 to-indigo-500 rounded-full transition-all duration-1000 ease-out relative overflow-hidden"
                  :style="`width: ${info.progress}%`"
              >
              </div>
            </div>
            <!-- 进度点 -->
            <div
                class="absolute top-1/2 transform -translate-y-1/2 w-4 h-4 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-full border-2 border-white shadow-lg animate-pulse-glow"
                :style="`left: calc(${info.progress}% - 8px)`"
            ></div>
          </div>
        </div>

        <!-- 巡检数据统计 -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="tech-stat-card bg-gradient-to-br from-emerald-50/90 to-green-50/90 backdrop-blur-sm rounded-xl p-4 flex items-center border border-emerald-200/50 hover:border-emerald-300/70 transition-all duration-300 group">
            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-emerald-100 to-green-100 flex items-center justify-center mr-4 border border-emerald-300/60 group-hover:scale-110 transition-transform">
              <Icon icon="mdi:check-circle" class="text-emerald-600 text-xl"></Icon>
            </div>
            <div>
              <p class="text-xs text-emerald-600 mb-1">合格数量</p>
              <p class="text-2xl font-bold text-gray-800 tech-number">{{ info.qualifiedCount }}</p>
            </div>
          </div>
          <div class="tech-stat-card bg-gradient-to-br from-amber-50/90 to-orange-50/90 backdrop-blur-sm rounded-xl p-4 flex items-center border border-amber-200/50 hover:border-amber-300/70 transition-all duration-300 group">
            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-amber-100 to-orange-100 flex items-center justify-center mr-4 border border-amber-300/60 group-hover:scale-110 transition-transform">
              <Icon icon="mdi:alert-circle" class="text-amber-600 text-xl"></Icon>
            </div>
            <div>
              <p class="text-xs text-amber-600 mb-1">待巡</p>
              <p class="text-2xl font-bold text-gray-800 tech-number">{{ info.pendingCount }}</p>
            </div>
          </div>
          <div class="tech-stat-card bg-gradient-to-br from-red-50/90 to-rose-50/90 backdrop-blur-sm rounded-xl p-4 flex items-center border border-red-200/50 hover:border-red-300/70 transition-all duration-300 group">
            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-red-100 to-rose-100 flex items-center justify-center mr-4 border border-red-300/60 group-hover:scale-110 transition-transform">
              <Icon icon="mdi:cancel" class="text-red-600 text-xl"></Icon>
            </div>
            <div>
              <p class="text-xs text-red-600 mb-1">漏检</p>
              <p class="text-2xl font-bold text-gray-800 tech-number">{{ info.missedCount }}</p>
            </div>
          </div>
          <div class="tech-stat-card bg-gradient-to-br from-cyan-50/90 to-blue-50/90 backdrop-blur-sm rounded-xl p-4 flex items-center border border-cyan-200/50 hover:border-cyan-300/70 transition-all duration-300 group">
            <div class="w-12 h-12 rounded-full bg-gradient-to-br from-cyan-100 to-blue-100 flex items-center justify-center mr-4 border border-cyan-300/60 group-hover:scale-110 transition-transform">
              <Icon icon="mdi:clipboard-check" class="text-cyan-600 text-xl"></Icon>
            </div>
            <div>
              <p class="text-xs text-cyan-600 mb-1">总巡更点</p>
              <p class="text-2xl font-bold text-gray-800 tech-number">{{ info.totalCount }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 左侧：巡检记录列表 -->
      <div class="lg:col-span-1">
        <div class="tech-card bg-gradient-to-br from-white/95 to-gray-50/95 backdrop-blur-xl rounded-2xl shadow-tech p-6 mb-6 transform transition-all duration-500 hover:shadow-tech-hover relative border border-cyan-500/20">
          <!-- 科技感装饰元素 -->
          <div class="absolute top-0 left-0 w-40 h-1 bg-gradient-to-r from-blue-500 via-cyan-400 to-transparent animate-pulse-slow"></div>
          <div class="absolute bottom-0 right-0 w-40 h-1 bg-gradient-to-r from-transparent via-cyan-400 to-blue-500 animate-pulse-slow"></div>
          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-semibold flex items-center text-gray-800">
              <Icon icon="mdi:format-list-checks" class="mr-2 text-cyan-600" size="24"></Icon>
              巡检记录列表
            </h3>
          </div>

          <div class="overflow-x-auto scrollbar-thin">
            <table class="min-w-full divide-y divide-cyan-500/20 rounded-lg overflow-hidden shadow-sm">
              <thead class="bg-cyan-50/80">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-cyan-700 uppercase tracking-wider">巡更点</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-cyan-700 uppercase tracking-wider">打卡次数</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-cyan-700 uppercase tracking-wider">巡更时间</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-cyan-700 uppercase tracking-wider">状态</th>
              </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-100">
              <tr class="hover:bg-cyan-50/50 transition-colors tech-list-item" v-for="(record, index) in info.planCardList" :key="index">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="w-8 h-8 rounded-full bg-gradient-to-br from-cyan-100 to-blue-100 flex items-center justify-center mr-3 border border-cyan-500/30">
                      <Icon icon="mdi:location" class="text-cyan-600"></Icon>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-800">{{ record.cardName }}</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="text-sm text-gray-600">{{ record.num }}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-600">{{ record.patrolTime }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span :class="getStatusBadgeClassLight(record.status)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                      {{ record.status == 0 ? '待巡' : record.status == 1 ? '正常' : record.status == 2 ? '漏检' : '未知状态' }}
                    </span>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>

      <!-- 视频监控 -->
      <div class="lg:col-span-1">
        <div class="tech-card bg-gradient-to-br from-white/95 to-gray-50/95 backdrop-blur-xl rounded-2xl shadow-tech p-6 mb-6 transform transition-all duration-500 hover:shadow-tech-hover relative border border-cyan-500/20">
          <!-- 科技感装饰元素 -->
          <div class="absolute top-0 left-0 w-40 h-1 bg-gradient-to-r from-blue-500 via-cyan-400 to-transparent animate-pulse-slow"></div>
          <div class="absolute bottom-0 right-0 w-40 h-1 bg-gradient-to-r from-transparent via-cyan-400 to-blue-500 animate-pulse-slow"></div>

          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold flex items-center text-gray-800">
              <Icon icon="mdi:video-wireless" class="mr-2 text-cyan-600" size="24"></Icon>
              视频监控
            </h3>
            <div class="flex items-center text-sm text-gray-600">
              <Icon icon="mdi:monitor-dashboard" class="mr-1.5 text-cyan-600" size="16"></Icon>
              高级监控模式
            </div>
          </div>

          <!-- 高级视频监控组件 -->
          <div class="advanced-video-monitor">
            <PlanVideoMonitorModal
                ref="videoMonitorRef"
                :plan-info="planInfo"
                :video-list="formattedVideoList"
                @error="onVideoMonitorError"
            />
          </div>

          <!-- 视频播放提示 -->
          <div v-if="formattedVideoList.length > 0" class="video-tips">
            <a-alert
              message="视频播放提示"
              description="点击上方工具栏的播放按钮开始播放视频，支持多路视频同时播放。如遇播放问题，请检查网络连接和视频源地址。"
              type="info"
              show-icon
              closable
            />
          </div>

          <!-- 视频监控状态显示 -->
          <div v-if="videoMonitorLoading" class="text-center py-8">
            <a-spin size="large">
              <template #indicator>
                <Icon icon="mdi:loading" class="animate-spin text-cyan-600" size="32"></Icon>
              </template>
            </a-spin>
            <p class="text-gray-600 mt-2">正在加载视频监控...</p>
          </div>

          <div v-else-if="videoMonitorError" class="text-center py-8">
            <Icon icon="mdi:alert-circle" size="48" class="text-red-400 mb-2"></Icon>
            <p class="text-red-600">{{ videoMonitorError }}</p>
            <a-button type="primary" size="small" @click="detail(info.id)" class="mt-2">
              重新加载
            </a-button>
          </div>

          <div v-else-if="!info.lineVideoList || info.lineVideoList.length === 0" class="text-center py-8 bg-gray-50 rounded-lg backdrop-blur-sm">
            <Icon icon="mdi:video-off" size="48" class="text-cyan-300 mb-2"></Icon>
            <p class="text-gray-500">暂无视频监控</p>
          </div>
        </div>
      </div>
      <!-- 打卡记录列表 -->
      <div class="lg:col-span-1">
        <div class="tech-card bg-gradient-to-br from-white/95 to-gray-50/95 backdrop-blur-xl rounded-2xl shadow-tech p-6 mb-6 transform transition-all duration-500 hover:shadow-tech-hover relative border border-cyan-500/20">
          <!-- 科技感装饰元素 -->
          <div class="absolute top-0 right-0 w-40 h-1 bg-gradient-to-r from-transparent via-cyan-400 to-blue-500 animate-pulse-slow"></div>
          <div class="absolute bottom-0 left-0 w-40 h-1 bg-gradient-to-r from-blue-500 via-cyan-400 to-transparent animate-pulse-slow"></div>

          <div class="flex justify-between items-center mb-6">
            <h3 class="text-lg font-semibold flex items-center text-gray-800">
              <Icon icon="mdi:clipboard-check-outline" class="mr-2 text-cyan-600" size="24"></Icon>
              打卡记录列表
            </h3>
            <div>
              <a-button type="link" @click="loadMoreCheckRecords" v-if="hasMoreCheckRecords" class="text-cyan-600 hover:text-cyan-700">
                加载更多 <Icon icon="mdi:chevron-down" />
              </a-button>
            </div>
          </div>

          <!-- 打卡记录列表 -->
          <a-list :data-source="info.recordList" :item-layout="'vertical'" class="patrolRecord-list">
            <template #renderItem="{ item }">
              <a-list-item class="hover:bg-cyan-50/50 transition-colors rounded-lg border border-cyan-100 shadow-tech hover:shadow-tech-hover mb-4 overflow-hidden relative group tech-list-item">
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-500/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <a-list-item-meta>
                  <template #title>
                    <div class="flex items-center justify-between">
                      <span class="font-medium text-gray-800">{{ item.cardName }}</span>
                      <span :class="getStatusBadgeClassLight(item.status)" class="px-3 py-1 rounded-full text-xs font-medium shadow-sm">
                          {{ item.status == 0 ? '待巡' : item.status == 1 ? '正常' : item.status == 2 ? '漏检' : '未知状态' }}
                        </span>
                    </div>
                  </template>
                  <template #description>
                    <div class="flex items-center text-gray-600">
                      <Icon icon="mdi:clock-outline" class="mr-2 text-cyan-600" size="16"></Icon>
                      <span>打卡时间：{{ item.recordTime }}</span>
                    </div>
                    <div v-if="item.remark" class="mt-2 bg-gray-50 p-3 rounded-lg text-sm backdrop-blur-sm">
                      <div class="font-medium mb-1 text-cyan-700">备注：</div>
                      <div class="text-gray-700">{{ item.remark }}</div>
                    </div>
                  </template>
                  <template #avatar>
                    <div class="w-12 h-12 rounded-full bg-gradient-to-br from-cyan-100 to-blue-100 flex items-center justify-center border border-cyan-200">
                      <Icon icon="mdi:map-marker-check" class="text-cyan-600" size="24"></Icon>
                    </div>
                  </template>
                </a-list-item-meta>

                <!-- 巡更截图 -->
                <div v-if="item.screenshots && item.screenshots.length > 0" class="mt-3 rounded-lg bg-gray-50 p-4 backdrop-blur-sm">
                  <div class="text-sm font-medium mb-3 text-cyan-700 flex items-center">
                    <Icon icon="mdi:camera" class="mr-2 text-cyan-600" size="18"></Icon>
                    巡更截图
                  </div>
                  <div class="grid grid-cols-2 sm:grid-cols-6 gap-3">
                    <div
                        v-for="(image, imgIndex) in item.screenshots.slice(0, 6)"
                        :key="imgIndex"
                        class="relative rounded-lg overflow-hidden cursor-pointer aspect-square group shadow-tech hover:shadow-tech-hover transition-all duration-300"
                        @click="previewImages(item.screenshots, imgIndex)"
                    >
                      <!-- 截图 -->
                      <img
                          :src="getFileAccessHttpUrl(image.screenshotPath || image.screenshotPath || image)"
                          class="w-full h-full object-cover"
                          alt="巡更截图"
                      />

                      <!-- 监控点信息覆盖层 -->
                      <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-2 transform translate-y-full group-hover:translate-y-0 transition-transform">
                        <div class="text-white text-xs truncate">
                          <Icon icon="mdi:cctv" class="mr-1" size="12"></Icon>
                          {{ image.videoName|| '未知监控点' }}
                        </div>
                      </div>

                      <!-- 更多指示器 -->
                      <div v-if="imgIndex === 5 && item.screenshots.length > 6" class="absolute inset-0 bg-black/40 flex items-center justify-center">
                        <span class="text-white text-sm font-medium">+{{ item.screenshots.length - 6 }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </a-list-item>
            </template>
            <template #empty>
              <div class="text-center py-8 bg-gray-50 rounded-lg backdrop-blur-sm">
                <Icon icon="mdi:clipboard-text-off" size="40" class="text-cyan-300 mb-2"></Icon>
                <p class="text-gray-500">暂无打卡记录</p>
              </div>
            </template>
          </a-list>
        </div>
      </div>

    </div>

  </j-modal>
</template>



<script setup lang="ts">
import {nextTick, ref, defineExpose, computed, defineEmits} from "vue"
import JModal from "@/components/Modal/src/JModal/JModal.vue";
import { detailUrl } from "../Plan.api";
import { Icon } from '/@/components/Icon';
import { defHttp } from '/@/utils/http/axios';
import { createImgPreview } from '/@/components/Preview/index';
import { getFileAccessHttpUrl } from '/@/utils/common/compUtils';
import PlanVideoMonitorModal from './PlanVideoMonitorModal.vue';
import Hls from 'hls.js';



const width = ref<number>(1400);
const title = ref<string>('详情');
const open = ref<boolean>(false)
const info = ref<any>([]);
const emit = defineEmits(['register', 'success']);

// 视频监控相关 - 现在只使用高级模式
const videoMonitorRef = ref();

// 巡更记录详情
const recordDetailVisible = ref(false);
const currentRecord = ref<any>(null);
const recordDetailList = ref<any[]>([]);

// 添加视频缩略图和打卡记录相关的数据
const checkRecordsList = ref<any[]>([]);
const hasMoreCheckRecords = ref(false);
const checkRecordsPage = ref(1);
const checkRecordsPageSize = ref(5);

// 视频监控状态
const videoMonitorLoading = ref(false);
const videoMonitorError = ref('');

// 添加必要的变量以避免编译错误
const hlsInstances = ref<any[]>([]);
const videoElements = ref<any[]>([]);
const allVideosPlaying = ref(false);



// 计算属性
const planInfo = computed(() => {
  if (!info.value) return null;
  return {
    id: info.value.id,
    name: info.value.name
  };
});

const formattedVideoList = computed(() => {
  if (!info.value?.lineVideoList) return [];

  return info.value.lineVideoList.map((video, index) => {
    const streamId = `plan_${info.value.id}_video_${video.id || index}`;
    return {
      id: video.id || `video_${index}`,
      name: video.name || `视频监控${index + 1}`,
      videoUrl: video.videoUrl || video.url,
      streamId: streamId,
      websocketUrl: `/websocket/video/${streamId}`,
      hlsUrl: `/jeecgboot/video/hls/${streamId}/index.m3u8`,
      streamType: video.streamType || 'preview',
      // 添加更多调试信息
      originalVideo: video
    };
  });
});

// 删除了网格模式相关的setVideoRef函数

function showModal(record) {
  open.value = true
  detail(record.id)
}

function detail(id) {
  videoMonitorLoading.value = true;
  videoMonitorError.value = '';

  detailUrl({id: id})
      .then((res) => {
        if (res.success) {
          info.value = res.result
          // 初始化视频列表的播放状态
          if (info.value.lineVideoList) {
            info.value.lineVideoList = info.value.lineVideoList.map(video => ({
              ...video,
              isPlaying: false,
              thumbnailUrl: null // 添加缩略图URL字段
            }));

            // 视频数据加载完成后立即初始化缩略图
            nextTick(() => {
              console.log('初始化视频缩略图，视频数量:', info.value.lineVideoList.length);
              initAllVideoThumbnails();
            });
          } else {
            videoMonitorLoading.value = false;
          }

          // 加载打卡记录
          loadCheckRecords();
        } else {
          console.log(res.message);
          videoMonitorError.value = res.message || '加载详情失败';
          videoMonitorLoading.value = false;
        }
      }).catch(error => {
    console.error('加载详情失败:', error);
    videoMonitorError.value = '加载详情失败';
    videoMonitorLoading.value = false;
  }).finally(() => {
    // 最终完成加载
    videoMonitorLoading.value = false;
  });
}

/**
 * 处理视频监控错误
 */
function onVideoMonitorError(error) {
  console.error('视频监控错误:', error);
  videoMonitorError.value = typeof error === 'string' ? error : '视频监控加载失败';
}

// 删除了网格模式相关的函数：
// - toggleVideoMode
// - toggleAllVideos
// - playAllVideos
// 现在只使用高级视频监控模式

// 删除了网格模式相关的函数：
// - pauseAllVideos
// - playVideo
// - checkAllVideosPlaying

// 删除了网格模式的HLS播放器初始化函数
// 现在使用高级视频监控模式，由PlanVideoMonitorModal组件处理视频播放

// 预览图片
const previewImage = (url) => {
  createImgPreview({
    imageList: [getFileAccessHttpUrl(url)]
  });
};

// 显示巡更记录详情
const showRecordDetail = (record) => {
  currentRecord.value = record;
  recordDetailVisible.value = true;

  // 生成详情列表数据
  recordDetailList.value = [{
    title: record.cardName || '未知巡更点',
    time: record.patrolTime || '未知时间',
    icon: 'mdi:map-marker',
    status: record.status,
    remark: record.remark || '',
    images: record.images || []
  }];

  // 如果有多次打卡记录，可以将它们添加到列表中
  if (record.checkRecords && record.checkRecords.length > 0) {
    record.checkRecords.forEach(check => {
      recordDetailList.value.push({
        title: `${record.cardName} - 打卡记录`,
        time: check.checkTime || '未知时间',
        icon: 'mdi:check-circle',
        status: check.status,
        remark: check.remark || '',
        images: check.images || []
      });
    });
  }
};

// 预览多张图片
const previewImages = (images, startIndex) => {
  if (!images || images.length === 0) return;

  const imageUrls = images.map(img => {
    // 处理不同类型的图片对象
    if (typeof img === 'string') {
      return getFileAccessHttpUrl(img);
    } else if (img.url) {
      return getFileAccessHttpUrl(img.url);
    } else if (img.screenshotPath) {
      return getFileAccessHttpUrl(img.screenshotPath);
    } else {
      return getFileAccessHttpUrl(img);
    }
  });

  createImgPreview({
    imageList: imageUrls,
    index: startIndex || 0
  });
};

// 关闭巡更记录详情
const closeRecordDetail = () => {
  recordDetailVisible.value = false;
  currentRecord.value = null;
};

const getStatusClass = (status) => {
  switch(status) {
    case '0':
    case 0:
      return 'bg-warning/10 text-warning';
    case '1':
    case 1:
      return 'bg-success/10 text-success';
    case '2':
    case 2:
      return 'bg-danger/10 text-danger';
    default:
      return 'bg-gray-100 text-gray-600';
  }
};

// 新增状态徽章样式函数（深色主题，保留备用）
const getStatusBadgeClass = (status) => {
  switch(status) {
    case '0':
    case 0:
      return 'bg-gradient-to-r from-amber-500/20 to-orange-500/20 text-amber-300 border-amber-500/40';
    case '1':
    case 1:
      return 'bg-gradient-to-r from-cyan-500/20 to-blue-500/20 text-cyan-300 border-cyan-500/40';
    case '2':
    case 2:
      return 'bg-gradient-to-r from-emerald-500/20 to-green-500/20 text-emerald-300 border-emerald-500/40';
    default:
      return 'bg-gradient-to-r from-gray-500/20 to-gray-600/20 text-gray-300 border-gray-500/40';
  }
};

// 新增状态点样式函数（深色主题，保留备用）
const getStatusDotClass = (status) => {
  switch(status) {
    case '0':
    case 0:
      return 'bg-amber-400';
    case '1':
    case 1:
      return 'bg-cyan-400';
    case '2':
    case 2:
      return 'bg-emerald-400';
    default:
      return 'bg-gray-400';
  }
};

// 新增浅色主题状态点样式函数
const getStatusDotClassLight = (status) => {
  switch(status) {
    case '0':
    case 0:
      return 'bg-amber-500';
    case '1':
    case 1:
      return 'bg-blue-500';
    case '2':
    case 2:
      return 'bg-emerald-500';
    default:
      return 'bg-gray-500';
  }
};

// 新增浅色主题状态徽章样式函数
const getStatusBadgeClassLight = (status) => {
  switch(status) {
    case '0':
    case 0:
      return 'bg-amber-50 text-amber-700 border border-amber-200';
    case '1':
    case 1:
      return 'bg-cyan-50 text-cyan-700 border border-cyan-200';
    case '2':
    case 2:
      return 'bg-emerald-50 text-emerald-700 border border-emerald-200';
    default:
      return 'bg-gray-50 text-gray-700 border border-gray-200';
  }
};

/**
 * 取消按钮回调时间
 */
function handleCancel() {
  open.value = false;

  // 销毁所有HLS实例
  hlsInstances.value.forEach((hls, index) => {
    if (hls) {
      hls.destroy();
    }
  });
  hlsInstances.value = [];

  // 重置所有视频的播放状态
  if (info.value.lineVideoList) {
    info.value.lineVideoList.forEach((video, index) => {
      video.isPlaying = false;
      if (videoElements.value[index]) {
        videoElements.value[index].pause();
        videoElements.value[index].src = '';
      }
    });
  }

  // 重置全局播放状态
  allVideosPlaying.value = false;
}

// 组件销毁时清理资源已移至handleCancel函数中

// 删除了网格模式的缩略图生成相关函数：
// - onVideoMetadataLoaded
// - generateThumbnailFromVideo (将在下面删除)

// 删除了generateThumbnailFromVideo函数，网格模式不再需要

// 加载打卡记录数据
const loadCheckRecords = () => {
  // 模拟数据，实际应该从API获取
  // 这里假设info.value.planCardList中的每条记录都有checkRecords字段包含打卡记录
  let allRecords: any[] = [];

  if (info.value.planCardList) {
    info.value.planCardList.forEach((card: any) => {
      if (card.checkRecords && card.checkRecords.length > 0) {
        // 给每条打卡记录添加巡更点信息
        const records = card.checkRecords.map((record: any) => ({
          ...record,
          cardName: card.cardName,
          cardId: card.id
        }));
        allRecords = [...allRecords, ...records];
      }
    });
  }

  // 如果没有checkRecords字段，创建一些模拟数据用于演示
  if (allRecords.length === 0 && info.value.planCardList) {
    allRecords = info.value.planCardList.filter(card => card.status == 1).map(card => ({
      id: `check-${card.id}`,
      cardId: card.id,
      cardName: card.cardName,
      checkTime: card.patrolTime,
      status: card.status,
      remark: '正常巡检',
      images: card.images || []
    }));
  }

  // 分页处理
  const totalRecords = allRecords.length;
  const startIndex = 0;
  const endIndex = checkRecordsPage.value * checkRecordsPageSize.value;

  checkRecordsList.value = allRecords.slice(startIndex, endIndex);
  hasMoreCheckRecords.value = endIndex < totalRecords;
};

// 加载更多打卡记录
const loadMoreCheckRecords = () => {
  checkRecordsPage.value += 1;
  loadCheckRecords();
};

// 修改初始化视频缩略图的函数，使用HLS.js处理视频
const initAllVideoThumbnails = () => {
  if (!info.value.lineVideoList || info.value.lineVideoList.length === 0) return;

  console.log('开始主动加载所有视频缩略图');

  // 遍历所有视频并创建临时视频元素生成缩略图
  info.value.lineVideoList.forEach((video, index) => {
    if (video.thumbnailUrl) return; // 已有缩略图则跳过

    console.log(`为视频 ${index} 创建临时视频元素生成缩略图`);

    // 创建临时视频元素
    const tempVideo = document.createElement('video');
    tempVideo.crossOrigin = 'anonymous';
    tempVideo.preload = 'metadata';
    tempVideo.muted = true;
    tempVideo.style.display = 'none';
    document.body.appendChild(tempVideo);

    // 使用HLS.js加载视频 - 支持.m3u8格式和openUrl格式
    const videoUrl = video.videoUrl || video.url;
    const isHlsVideo = videoUrl && (videoUrl.includes('.m3u8') || videoUrl.includes('openUrl'));

    if (Hls.isSupported() && isHlsVideo) {
      console.log(`视频 ${index} 是HLS格式，使用HLS.js加载`);
      const hls = new Hls({
        // 缩略图生成不需要太高质量，可以选择较低的配置
        maxBufferLength: 5,
        maxMaxBufferLength: 10,
        enableWorker: false,
        lowLatencyMode: false
      });

      // 存储HLS实例以便后续清理
      const tempHlsInstance = hls;

      hls.loadSource(videoUrl);
      hls.attachMedia(tempVideo);

      hls.on(Hls.Events.MANIFEST_PARSED, () => {
        console.log(`视频 ${index} HLS清单解析完成，准备生成缩略图`);
        // 设置超时，确保有足够时间加载视频帧
        setTimeout(() => {
          try {
            tempVideo.currentTime = 1;

            // 监听定位完成事件
            tempVideo.onseeked = () => {
              console.log(`视频 ${index} 已定位到指定时间点，准备生成缩略图`);
              generateThumbnailFromVideo(tempVideo, index);

              // 清理资源
              if (tempHlsInstance) {
                tempHlsInstance.destroy();
              }
              if (document.body.contains(tempVideo)) {
                document.body.removeChild(tempVideo);
              }
            };
          } catch (e) {
            console.error(`无法设置HLS视频 ${index} 时间点:`, e);
            // 清理资源
            if (tempHlsInstance) {
              tempHlsInstance.destroy();
            }
            if (document.body.contains(tempVideo)) {
              document.body.removeChild(tempVideo);
            }
          }
        }, 1000); // 给HLS解析一些时间
      });

      // 添加错误处理
      hls.on(Hls.Events.ERROR, (_, data) => {
        console.error(`HLS视频 ${index} 加载错误:`, data);
        if (data.fatal) {
          // 清理资源
          tempHlsInstance.destroy();
          if (document.body.contains(tempVideo)) {
            document.body.removeChild(tempVideo);
          }

          // 尝试从封面图生成缩略图
          if (video.image) {
            console.log(`尝试从封面图生成视频 ${index} 的缩略图`);
            generateThumbnailFromImage(video.image, index);
          }
        }
      });
    } else {
      // 非HLS视频或者不支持HLS.js，使用普通方式加载
      console.log(`视频 ${index} 使用普通方式加载`);

      // 设置事件监听器
      tempVideo.onloadedmetadata = () => {
        console.log(`视频 ${index} 元数据已加载，设置时间点`);
        try {
          tempVideo.currentTime = 1; // 设置到1秒位置
        } catch (e) {
          console.error(`无法设置视频 ${index} 时间点:`, e);
        }
      };

      tempVideo.onseeked = () => {
        console.log(`视频 ${index} 已定位到指定时间点，准备生成缩略图`);
        generateThumbnailFromVideo(tempVideo, index);

        // 任务完成后移除临时元素
        if (document.body.contains(tempVideo)) {
          document.body.removeChild(tempVideo);
        }
      };

      tempVideo.onerror = (e) => {
        console.error(`视频 ${index} 加载失败:`, e);
        if (document.body.contains(tempVideo)) {
          document.body.removeChild(tempVideo);
        }

        // 尝试从封面图生成缩略图
        if (video.image) {
          console.log(`尝试从封面图生成视频 ${index} 的缩略图`);
          generateThumbnailFromImage(video.image, index);
        }
      };

      // 设置视频源
      tempVideo.src = video.url || video.videoUrl;
    }
  });
};

// 从视频生成缩略图的函数
const generateThumbnailFromVideo = (videoElement: HTMLVideoElement, index: number) => {
  try {
    console.log(`开始为视频 ${index} 生成缩略图`);

    const canvas = document.createElement('canvas');
    canvas.width = videoElement.videoWidth || 320;
    canvas.height = videoElement.videoHeight || 240;

    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error(`无法获取Canvas上下文`);
      return;
    }

    // 绘制视频帧到canvas
    ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

    try {
      // 转换为base64图片URL
      const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.8);
      console.log(`视频 ${index} 缩略图生成成功`);

      // 更新视频列表中的缩略图URL
      if (info.value.lineVideoList && info.value.lineVideoList[index]) {
        info.value.lineVideoList[index].thumbnailUrl = thumbnailUrl;
      }
    } catch (e) {
      console.error(`转换视频缩略图失败:`, e);
    }
  } catch (error) {
    console.error(`生成视频 ${index} 缩略图失败:`, error);
  }
};

// 从图片生成缩略图的新函数
const generateThumbnailFromImage = (imageUrl: string, index: number) => {
  console.log(`从图片生成视频 ${index} 的缩略图`);

  // 创建临时图片元素
  const img = new Image();
  img.crossOrigin = 'anonymous';

  img.onload = () => {
    try {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.error(`无法获取Canvas上下文`);
        return;
      }
      ctx.drawImage(img, 0, 0);

      try {
        // 转换为base64图片URL
        const thumbnailUrl = canvas.toDataURL('image/jpeg');
        console.log(`从图片生成视频 ${index} 缩略图成功`);

        if (info.value.lineVideoList && info.value.lineVideoList[index]) {
          info.value.lineVideoList[index].thumbnailUrl = thumbnailUrl;
        }
      } catch (e) {
        console.error(`转换图片缩略图失败:`, e);
      }
    } catch (e) {
      console.error(`绘制图片到Canvas失败:`, e);
    }
  };

  img.onerror = () => {
    console.error(`加载图片失败:`, imageUrl);
  };

  // 设置图片源
  img.src = getFileAccessHttpUrl(imageUrl);
};

// 测试新视频格式的函数
const testVideoFormat = () => {
  // 测试URL格式：http://10.2.145.79:554/openUrl/3whQVPi.m3u8?beginTime=20170615T000000&endTime=20170617T000000&playBackMode=1
  const testVideoUrl = "http://10.2.145.79:554/openUrl/3whQVPi.m3u8?beginTime=20170615T000000&endTime=20170617T000000&playBackMode=1";

  console.log('测试视频URL格式支持:', testVideoUrl);

  // 检查是否识别为HLS格式
  const isHlsVideo = testVideoUrl && (testVideoUrl.includes('.m3u8') || testVideoUrl.includes('openUrl'));
  console.log('是否识别为HLS格式:', isHlsVideo);

  // 检查HLS.js支持
  console.log('HLS.js是否支持:', Hls.isSupported());

  return {
    url: testVideoUrl,
    isHlsFormat: isHlsVideo,
    hlsSupported: Hls.isSupported()
  };
};

defineExpose({
  showModal,
  detail,
  testVideoFormat
});
</script>
<style scoped lang="less">
/* 科技感容器 */
.tech-container {
  position: relative;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 科技网格背景 */
.tech-grid-bg {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
      linear-gradient(rgba(59, 130, 246, 0.08) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.08) 1px, transparent 1px),
      linear-gradient(rgba(6, 182, 212, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(6, 182, 212, 0.05) 1px, transparent 1px);
  background-size: 50px 50px, 50px 50px, 10px 10px, 10px 10px;
  pointer-events: none;
  z-index: -1;
}

/* 科技卡片 */
.tech-card {
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
}



/* 科技信息卡片 */
.tech-info-card {
  position: relative;
  overflow: hidden;
}



/* 科技时间卡片 */
.tech-time-card {
  position: relative;
  overflow: hidden;
}



/* 科技统计卡片 */
.tech-stat-card {
  position: relative;
  overflow: hidden;
}



/* 科技头像 */
.tech-avatar {
  position: relative;
  overflow: hidden;
}



/* 科技数字 */
.tech-number {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, currentColor, rgba(59, 130, 246, 0.8));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 科技进度容器 */
.tech-progress-container {
  position: relative;
}

/* 文字科技感阴影效果 */
.text-shadow-tech {
  text-shadow: 0 2px 4px rgba(59, 130, 246, 0.2), 0 1px 2px rgba(6, 182, 212, 0.1);
}

/* 科技阴影 */
.shadow-tech {
  box-shadow:
      0 4px 20px rgba(59, 130, 246, 0.12),
      0 2px 10px rgba(6, 182, 212, 0.08),
      0 1px 3px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.shadow-tech-hover {
  box-shadow:
      0 8px 30px rgba(59, 130, 246, 0.18),
      0 4px 20px rgba(6, 182, 212, 0.12),
      0 2px 8px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 动画定义 */

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.4);
    border-color: rgba(59, 130, 246, 0.4);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
    border-color: rgba(59, 130, 246, 0.6);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px currentColor;
  }
  50% {
    box-shadow: 0 0 15px currentColor, 0 0 25px currentColor;
  }
}

@keyframes pulse-slow {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}



.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 科技状态徽章 */
.tech-status-badge {
  position: relative;
  overflow: hidden;
}



.content-auto {
  content-visibility: auto;
}
.bg-grid {
  background-image: linear-gradient(rgba(22, 93, 255, 0.05) 1px, transparent 1px),
  linear-gradient(90deg, rgba(22, 93, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
}
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
.scrollbar-thin {
  scrollbar-width: thin;
}
.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}
.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
}
.bg-primary-light {
  --tw-bg-opacity: 1;
  background-color: rgb(232 243 255 / var(--tw-bg-opacity, 1));
}
.text-primary {
  --tw-text-opacity: 1;
  color: rgb(22 93 255 / var(--tw-text-opacity, 1));
}
.bg-success-light {
  --tw-bg-opacity: 1;
  background-color: rgb(232 255 243 / var(--tw-bg-opacity, 1));
}
.bg-warning-light {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 232 / var(--tw-bg-opacity, 1));
}
.bg-danger-light {
  --tw-bg-opacity: 1;
  background-color: rgb(255 232 232 / var(--tw-bg-opacity, 1));
}
.bg-primary {
  --tw-bg-opacity: 1;
  background-color: rgb(22 93 255 / var(--tw-bg-opacity, 1));
}
.text-success {
  --tw-text-opacity: 1;
  color: rgb(0 180 42 / var(--tw-text-opacity, 1));
}
.text-warning {
  --tw-text-opacity: 1;
  color: rgb(255 125 0 / var(--tw-text-opacity, 1));
}
.text-danger {
  --tw-text-opacity: 1;
  color: rgb(245 63 63 / var(--tw-text-opacity, 1));
}
.bg-danger {
  --tw-bg-opacity: 1;
  background-color: rgb(245 63 63 / var(--tw-bg-opacity, 1));
}
.bg-success {
  --tw-bg-opacity: 1;
  background-color: rgb(0 180 42 / var(--tw-bg-opacity, 1));
}
.bg-light-blue {
  --tw-bg-opacity: 1;
  background-color: rgb(232 243 255 / var(--tw-bg-opacity, 1));
}
.font-medium{
  font-weight: 500;
}

.bg-primary\/10 {
  background-color: rgb(22 93 255 / 0.1);
}
.bg-warning\/10 {
  background-color: rgb(255 125 0 / 0.1);
}
.bg-danger\/10 {
  background-color: rgb(245 63 63 / 0.1);
}
.bg-secondary\/10 {
  background-color: rgb(15 198 194 / 0.1);
}
.bg-primary\/80 {
  background-color: rgb(22 93 255 / 0.8);
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.bg-success\/10 {
  background-color: rgb(0 180 42 / 0.1);
}
.bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));
}
.shadow-card {
  box-shadow: 0 4px 15px rgba(22, 93, 255, 0.05);
}

.shadow-card-hover {
  box-shadow: 0 8px 25px rgba(22, 93, 255, 0.1);
}

.text-primary-light {
  color: rgb(118, 163, 255);
}

.patrolRecord-list {
  position: relative;
}

.patrolRecord-list::before {
  content: '';
  position: absolute;
  left: 35px;
  top: 0;
  bottom: 0;
  width: 1px;
  background: linear-gradient(to bottom,
  transparent 0%,
  rgba(22, 93, 255, 0.2) 10%,
  rgba(22, 93, 255, 0.2) 90%,
  transparent 100%);
  z-index: 1;
}

@keyframes pulse-border {
  0% {
    box-shadow: 0 0 0 0 rgba(22, 93, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(22, 93, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(22, 93, 255, 0);
  }
}

.animate-pulse-border {
  animation: pulse-border 2s infinite;
}

.patrolDetail-list {
  position: relative;
}

.patrolDetail-list::before {
  content: '';
  position: absolute;
  left: 35px;
  top: 70px; /* 标题下方开始 */
  bottom: 20px;
  width: 1px;
  background: linear-gradient(to bottom,
  rgba(22, 93, 255, 0.4) 0%,
  rgba(22, 93, 255, 0.2) 50%,
  transparent 100%);
  z-index: 1;
}

/* 科技模态框 */
.tech-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(249, 250, 251, 0.95) 100%);
  backdrop-filter: blur(20px);
  box-shadow:
      0 20px 40px rgba(6, 182, 212, 0.1),
      0 10px 25px rgba(59, 130, 246, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(6, 182, 212, 0.2);
}

.tech-modal .ant-modal-header {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(243, 244, 246, 0.95) 100%);
  border-bottom: 1px solid rgba(6, 182, 212, 0.1);
  backdrop-filter: blur(10px);
}

.tech-modal .ant-modal-title {
  color: #0e7490;
  text-shadow: 0 0 10px rgba(6, 182, 212, 0.2);
}

.tech-modal .ant-modal-close {
  color: rgba(6, 182, 212, 0.8);
}

.tech-modal .ant-modal-close:hover {
  color: #06b6d4;
}

.tech-modal .ant-modal-body {
  background: transparent;
  padding: 0;
}

.video-card {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.5), transparent);
    z-index: 5;
  }

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.5), transparent);
    z-index: 5;
  }
}

.play-button-pulse {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 1px solid rgba(6, 182, 212, 0.5);
    animation: pulse-border 1.5s infinite;
  }
}

.loading-bar {
  animation: loading 1.5s infinite;
  width: 30%;
}

@keyframes loading {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

.video-player {
  border-radius: 4px;
  box-shadow: 0 0 0 1px rgba(6, 182, 212, 0.1);
}

.cyber-button {
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%);
    transform: translateX(-100%);
    transition: transform 0.6s;
  }

  &:hover::before {
    transform: translateX(100%);
  }
}

.tech-list-item {
  transition: all 0.2s ease-out;

  &:hover {
    transform: translateX(3px);
  }
}

.patrolRecord-list {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    left: 35px;
    top: 0;
    bottom: 0;
    width: 1px;
    background: linear-gradient(to bottom,
    transparent 0%,
    rgba(6, 182, 212, 0.2) 10%,
    rgba(6, 182, 212, 0.2) 90%,
    transparent 100%);
    z-index: 1;
  }
}

.scrollbar-thin {
  scrollbar-width: thin;

  &::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(6, 182, 212, 0.05);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(6, 182, 212, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(6, 182, 212, 0.5);
  }
}

/* 浅色科技感增强样式 */
.tech-container {
  background:
      radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(6, 182, 212, 0.05) 0%, transparent 50%),
      linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* 增强的科技感卡片 */
.tech-card {
  background:
      linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.tech-card:hover {
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-2px);
}

/* 科技感按钮增强 */
.cyber-button {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.9) 0%, rgba(6, 182, 212, 0.9) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
      0 4px 15px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.cyber-button:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 1) 0%, rgba(6, 182, 212, 1) 100%);
  box-shadow:
      0 6px 20px rgba(59, 130, 246, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 科技感模态框增强 */
.tech-modal .ant-modal-content {
  background:
      linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%);
  border: 1px solid rgba(59, 130, 246, 0.15);
  box-shadow:
      0 25px 50px rgba(59, 130, 246, 0.1),
      0 10px 30px rgba(6, 182, 212, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

/* 科技感进度条 */
.tech-progress-container .bg-gradient-to-r {
  background: linear-gradient(90deg,
  rgba(59, 130, 246, 0.8) 0%,
  rgba(6, 182, 212, 0.9) 50%,
  rgba(99, 102, 241, 0.8) 100%);
  box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
}

/* 科技感统计卡片增强 */
.tech-stat-card:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow:
      0 10px 25px rgba(59, 130, 246, 0.15),
      0 5px 15px rgba(6, 182, 212, 0.1);
}

/* 高级视频监控样式 */
.advanced-video-monitor {
  min-height: 400px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border: 1px solid rgba(99, 102, 241, 0.1);
}

.advanced-video-monitor :deep(.video-monitor-grid) {
  background: transparent;
}

.advanced-video-monitor :deep(.monitor-toolbar) {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(99, 102, 241, 0.1);
}

.advanced-video-monitor :deep(.video-grid) {
  background: transparent;
  padding: 12px;
}

.advanced-video-monitor :deep(.video-grid-item) {
  border: 1px solid rgba(99, 102, 241, 0.2);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.advanced-video-monitor :deep(.video-grid-item:hover) {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  border-color: rgba(99, 102, 241, 0.4);
}

// /**  视频模式切换按钮样式 */
.cyber-button {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.cyber-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.cyber-button:hover::before {
  left: 100%;
}

.cyber-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 视频播放提示样式 */
.video-tips {
  margin-top: 16px;
  padding: 0 16px;
}

.video-tips .ant-alert {
  border-radius: 8px;
  border: 1px solid rgba(6, 182, 212, 0.2);
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.05) 0%, rgba(59, 130, 246, 0.05) 100%);
}

.video-tips .ant-alert-message {
  color: #0e7490;
  font-weight: 500;
}

.video-tips .ant-alert-description {
  color: #0891b2;
}
</style>