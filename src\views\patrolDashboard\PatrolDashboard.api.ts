import { defHttp } from '/@/utils/http/axios';

enum Api {
  getDashboardData = '/plan/plan/divide-prison',
  getTodayStaff = '/patrol/dashboard/todayStaff',
  getPatrolPlans = '/patrol/dashboard/patrolPlans',
  getPatrolPlanDetail = '/patrol/dashboard/planDetail',
  updatePatrolStatus = '/patrol/dashboard/updateStatus',
  getUserManagedLines = '/plan/plan/getUserManagedLines',
}

/**
 * 获取分监区大屏数据
 * @param params
 */
export const getPatrolDashboardData = (params?: any) => {
  return defHttp.get({ 
    url: Api.getDashboardData, 
    params: params || {} 
  });
};

/**
 * 获取今日巡更人员信息
 * @param params
 */
export const getTodayStaff = (params?: any) => {
  return defHttp.get({ 
    url: Api.getTodayStaff, 
    params: params || {} 
  });
};

/**
 * 获取巡更计划列表
 * @param params
 */
export const getPatrolPlans = (params?: any) => {
  return defHttp.get({ 
    url: Api.getPatrolPlans, 
    params: params || {} 
  });
};

/**
 * 获取巡更计划详情
 * @param planId
 */
export const getPatrolPlanDetail = (planId: string) => {
  return defHttp.get({ 
    url: Api.getPatrolPlanDetail, 
    params: { planId } 
  });
};

/**
 * 更新巡更状态
 * @param params
 */
export const updatePatrolStatus = (params: any) => {
  return defHttp.post({ 
    url: Api.updatePatrolStatus, 
    params 
  });
};

/**
 * 
 * @param params 判断登录用户是否存在管理路线
 * @returns 
 */
export const UserManagedLines = (params) => defHttp.get({ url: Api.getUserManagedLines, params },{ isTransformResponse: false });

// 数据类型定义
export interface StaffInfo {
  id: string | number;
  name: string;
  cardId: string;
  avatar?: string;
  status: 'online' | 'offline' | 'on-duty';
  isOnDuty: boolean;
  department?: string;
  position?: string;
}

export interface PatrolPoint {
  id: string | number;
  name: string;
  status: 'checked' | 'current' | 'pending' | 'missed';
  checkTime?: string;
  location?: {
    x: number;
    y: number;
  };
  description?: string;
}

export interface PatrolPlan {
  id: string | number;
  name?: string;
  sectionName: string;
  status: 'in-progress' | 'pending' | 'completed' | 'missed';
  startTime: string;
  endTime: string;
  estimatedDuration: string;
  currentDuration?: string;
  actualDuration?: string;
  staff: StaffInfo;
  points: PatrolPoint[];
  priority?: 'high' | 'medium' | 'low';
  notes?: string;
}

export interface DashboardData {
  sectionName: string;
  todayStaff: StaffInfo[];
  patrolPlans: PatrolPlan[];
  statistics?: {
    totalPlans: number;
    completedPlans: number;
    inProgressPlans: number;
    missedPlans: number;
    totalStaff: number;
    onDutyStaff: number;
  };
}

// 模拟数据生成函数（用于开发测试）
// 新增监区可视化大屏数据接口
export const getPrisonVisualizationData = (params?: any) => {
  return defHttp.get({
    url: '/patrol/visualization/data',
    params: params || {}
  });
};

// 监区可视化数据结构
export interface PrisonSection {
  id: string | number;
  name: string;
  staff: StaffInfo[];
  patrolPlans: PatrolPlan[];
}

export interface PrisonVisualizationData {
  prisonName: string;
  sections: PrisonSection[];
  totalStatistics: {
    totalStaff: number;
    onDutyStaff: number;
    totalPlans: number;
    inProgressPlans: number;
    completedPlans: number;
    missedPlans: number;
  };
}

// 生成人员数据的辅助函数
const generateStaff = (sectionName: string, startId: number): StaffInfo[] => [
  {
    id: startId,
    name: `${sectionName.slice(0, 3)}张三`,
    cardId: `P${String(startId).padStart(3, '0')}`,
    avatar: '',
    status: 'on-duty',
    isOnDuty: true,
    department: sectionName,
    position: '巡更员'
  },
  {
    id: startId + 1,
    name: `${sectionName.slice(0, 3)}李四`,
    cardId: `P${String(startId + 1).padStart(3, '0')}`,
    avatar: '',
    status: 'online',
    isOnDuty: false,
    department: sectionName,
    position: '巡更员'
  },
  {
    id: startId + 2,
    name: `${sectionName.slice(0, 3)}王五`,
    cardId: `P${String(startId + 2).padStart(3, '0')}`,
    avatar: '',
    status: 'offline',
    isOnDuty: false,
    department: sectionName,
    position: '巡更员'
  }
];

// 生成巡更计划数据的辅助函数
const generatePatrolPlans = (sectionName: string, staff: StaffInfo[], startId: number): PatrolPlan[] => {
  const plans: PatrolPlan[] = [];
  const statuses: ('in-progress' | 'pending' | 'completed' | 'missed')[] = ['in-progress', 'pending', 'completed'];

  for (let i = 0; i < 3; i++) {
    const planId = startId + i;
    const status = statuses[i % statuses.length];
    const staffMember = staff[i % staff.length];

    // 生成12个巡更点
    const points: PatrolPoint[] = [
      { id: planId * 100 + 1, name: '活动室', status: status === 'completed' ? 'checked' : (status === 'in-progress' && i === 0 ? 'checked' : 'pending') },
      { id: planId * 100 + 2, name: '1号室', status: status === 'completed' ? 'checked' : (status === 'in-progress' && i === 0 ? 'checked' : 'pending') },
      { id: planId * 100 + 3, name: '2号室', status: status === 'completed' ? 'checked' : (status === 'in-progress' && i === 0 ? 'checked' : 'pending') },
      { id: planId * 100 + 4, name: '3号室', status: status === 'completed' ? 'checked' : (status === 'in-progress' && i === 0 ? 'current' : 'pending') },
      { id: planId * 100 + 5, name: '4号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 6, name: '5号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 7, name: '6号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 8, name: '7号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 9, name: '8号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 10, name: '9号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 11, name: '10号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 12, name: '活动室', status: status === 'completed' ? 'checked' : 'pending' }
    ];

    plans.push({
      id: planId,
      name: `${sectionName}${['上午', '下午', '夜间'][i]}巡更`,
      sectionName,
      status,
      startTime: ['08:00', '14:00', '20:00'][i],
      endTime: ['10:00', '16:00', '22:00'][i],
      estimatedDuration: '2小时',
      currentDuration: status === 'in-progress' ? '45分钟' : undefined,
      actualDuration: status === 'completed' ? '1小时50分钟' : undefined,
      staff: staffMember,
      points,
      priority: 'high'
    });
  }

  return plans;
};

// 生成监区可视化模拟数据
export const generatePrisonVisualizationMockData = (): PrisonVisualizationData => {
  // 生成三个分监区数据
  const sections: PrisonSection[] = [
    {
      id: 1,
      name: '第一分监区',
      staff: generateStaff('第一分监区', 1),
      patrolPlans: []
    },
    {
      id: 2,
      name: '第二分监区',
      staff: generateStaff('第二分监区', 4),
      patrolPlans: []
    },
    {
      id: 3,
      name: '第三分监区',
      staff: generateStaff('第三分监区', 7),
      patrolPlans: []
    }
  ];

  // 为每个分监区生成巡更计划
  sections.forEach((section, index) => {
    section.patrolPlans = generatePatrolPlans(section.name, section.staff, (index + 1) * 10);
  });

  return {
    prisonName: '某某监狱',
    sections,
    totalStatistics: {
      totalStaff: 9,
      onDutyStaff: 3,
      totalPlans: 9,
      inProgressPlans: 3,
      completedPlans: 3,
      missedPlans: 0
    }
  };
};

export const generateMockData = (): DashboardData => {
  const mockStaff: StaffInfo[] = [
    {
      id: 1,
      name: '张三',
      cardId: 'P001',
      avatar: '',
      status: 'on-duty',
      isOnDuty: true,
      department: '第一分监区',
      position: '巡更员'
    },
    {
      id: 2,
      name: '李四',
      cardId: 'P002',
      avatar: '',
      status: 'online',
      isOnDuty: false,
      department: '第一分监区',
      position: '巡更员'
    },
    {
      id: 3,
      name: '王五',
      cardId: 'P003',
      avatar: '',
      status: 'offline',
      isOnDuty: false,
      department: '第一分监区',
      position: '巡更员'
    }
  ];

  const mockPlans: PatrolPlan[] = [
    {
      id: 1,
      name: '第一分监区夜间巡更',
      sectionName: '第一分监区',
      status: 'in-progress',
      startTime: '08:00',
      endTime: '10:00',
      estimatedDuration: '2小时',
      currentDuration: '45分钟',
      staff: mockStaff[0],
      priority: 'high',
      points: [
        { id: 1, name: '活动室', status: 'checked', checkTime: '08:00' },
        { id: 2, name: '1号室', status: 'checked', checkTime: '08:05' },
        { id: 3, name: '2号室', status: 'checked', checkTime: '08:10' },
        { id: 4, name: '3号室', status: 'checked', checkTime: '08:15' },
        { id: 5, name: '4号室', status: 'current' },
        { id: 6, name: '5号室', status: 'pending' },
        { id: 7, name: '6号室', status: 'pending' },
        { id: 8, name: '7号室', status: 'pending' },
        { id: 9, name: '8号室', status: 'pending' },
        { id: 10, name: '9号室', status: 'pending' },
        { id: 11, name: '10号室', status: 'pending' },
        { id: 12, name: '活动室', status: 'pending' }
      ]
    },
    {
      id: 2,
      name: '第一分监区日间巡更',
      sectionName: '第一分监区',
      status: 'pending',
      startTime: '10:00',
      endTime: '', // 测试没有结束时间的情况
      estimatedDuration: '2小时',
      staff: mockStaff[1],
      priority: 'medium',
      points: [
        { id: 13, name: '活动室', status: 'pending' },
        { id: 14, name: '11号室', status: 'pending' },
        { id: 15, name: '12号室', status: 'pending' },
        { id: 16, name: '13号室', status: 'pending' },
        { id: 17, name: '14号室', status: 'pending' },
        { id: 18, name: '15号室', status: 'pending' },
        { id: 19, name: '16号室', status: 'pending' },
        { id: 20, name: '活动室', status: 'pending' }
      ]
    },
    {
      id: 3,
      name: '第一分监区早班巡更',
      sectionName: '第一分监区',
      status: 'completed',
      startTime: '06:00',
      endTime: '08:00',
      estimatedDuration: '2小时',
      actualDuration: '1小时50分钟',
      staff: mockStaff[2],
      priority: 'medium',
      points: [
        { id: 25, name: '活动室', status: 'checked', checkTime: '06:00' },
        { id: 26, name: '21号室', status: 'checked', checkTime: '06:05' },
        { id: 27, name: '22号室', status: 'checked', checkTime: '06:10' },
        { id: 28, name: '23号室', status: 'checked', checkTime: '06:15' },
        { id: 29, name: '24号室', status: 'checked', checkTime: '06:20' },
        { id: 30, name: '25号室', status: 'checked', checkTime: '06:25' },
        { id: 31, name: '26号室', status: 'checked', checkTime: '06:30' },
        { id: 32, name: '27号室', status: 'checked', checkTime: '06:35' },
        { id: 33, name: '28号室', status: 'checked', checkTime: '06:40' },
        { id: 34, name: '29号室', status: 'checked', checkTime: '06:45' },
        { id: 35, name: '30号室', status: 'checked', checkTime: '06:50' },
        { id: 36, name: '活动室', status: 'checked', checkTime: '06:55' }
      ]
    },
    {
      id: 4,
      name: '第二分监区下午巡更',
      sectionName: '第二分监区',
      status: 'in-progress',
      startTime: '14:00',
      endTime: '16:00',
      estimatedDuration: '2小时',
      currentDuration: '30分钟',
      staff: mockStaff[0],
      priority: 'high',
      points: [
        { id: 37, name: '大厅', status: 'checked', checkTime: '14:00' },
        { id: 38, name: 'A区', status: 'checked', checkTime: '14:10' },
        { id: 39, name: 'B区', status: 'current' },
        { id: 40, name: 'C区', status: 'pending' },
        { id: 41, name: 'D区', status: 'pending' },
        { id: 42, name: '大厅', status: 'pending' }
      ]
    }
  ];

  return {
    sectionName: '第一分监区',
    todayStaff: mockStaff,
    patrolPlans: mockPlans,
    statistics: {
      totalPlans: 8,
      completedPlans: 3,
      inProgressPlans: 2,
      missedPlans: 1,
      totalStaff: 6,
      onDutyStaff: 3
    }
  };
};
