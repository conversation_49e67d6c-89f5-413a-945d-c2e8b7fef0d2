<template>
  <div class="test-page">
    <h1>巡更点和连接线测试页面</h1>
    
    <div class="test-section">
      <h2>巡更点状态测试</h2>
      <div class="patrol-points-test">
        <div class="patrol-point status-checked">
          <div class="point-icon icon-checked">
            <Icon icon="ant-design:check-circle-filled" />
          </div>
          <div class="point-info">
            <div class="point-name">活动室</div>
            <div class="point-time">08:00</div>
          </div>
          <div class="point-connector">
            <div class="connector-line"></div>
          </div>
        </div>
        
        <div class="patrol-point status-checked">
          <div class="point-icon icon-checked">
            <Icon icon="ant-design:check-circle-filled" />
          </div>
          <div class="point-info">
            <div class="point-name">1号室</div>
            <div class="point-time">08:05</div>
          </div>
          <div class="point-connector">
            <div class="connector-line"></div>
          </div>
        </div>
        
        <div class="patrol-point status-current is-current">
          <div class="point-icon icon-current">
            <Icon icon="ant-design:environment-filled" />
          </div>
          <div class="point-info">
            <div class="point-name">2号室</div>
            <div class="point-time"></div>
          </div>
          <div class="point-connector">
            <div class="connector-line"></div>
          </div>
        </div>
        
        <div class="patrol-point status-pending is-next">
          <div class="point-icon icon-pending">
            <Icon icon="ant-design:clock-circle-outlined" />
          </div>
          <div class="point-info">
            <div class="point-name">3号室</div>
            <div class="point-time"></div>
          </div>
          <div class="point-connector">
            <div class="connector-line"></div>
          </div>
        </div>
        
        <div class="patrol-point status-pending">
          <div class="point-icon icon-pending">
            <Icon icon="ant-design:clock-circle-outlined" />
          </div>
          <div class="point-info">
            <div class="point-name">4号室</div>
            <div class="point-time"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="test-section">
      <h2>状态说明</h2>
      <div class="status-legend">
        <div class="legend-item">
          <div class="point-icon icon-checked">
            <Icon icon="ant-design:check-circle-filled" />
          </div>
          <span>已巡更 - 绿色渐变，打勾图标</span>
        </div>
        <div class="legend-item">
          <div class="point-icon icon-current">
            <Icon icon="ant-design:environment-filled" />
          </div>
          <span>当前位置 - 蓝色渐变，位置图标，脉冲动画</span>
        </div>
        <div class="legend-item">
          <div class="point-icon icon-pending">
            <Icon icon="ant-design:clock-circle-outlined" />
          </div>
          <span>待巡更 - 黄色渐变，时钟图标</span>
        </div>
        <div class="legend-item">
          <div class="point-icon icon-missed">
            <Icon icon="ant-design:close-circle-filled" />
          </div>
          <span>漏巡 - 红色渐变，叉号图标</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Icon } from '/@/components/Icon';
</script>

<style lang="less" scoped>
.test-page {
  padding: 40px;
  background: linear-gradient(135deg, #0a0e27 0%, #1a1f3a 50%, #0a0e27 100%);
  color: #ffffff;
  min-height: 100vh;
  
  h1, h2 {
    color: #00ffff;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
  }
}

.test-section {
  margin-bottom: 60px;
  padding: 30px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 15px;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.patrol-points-test {
  display: flex;
  align-items: center;
  overflow-x: auto;
  padding: 15px 0;
  position: relative;

  .patrol-point {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 70px;
    position: relative;
    flex-shrink: 0;
    margin-right: 35px;

    &:last-child {
      margin-right: 0;
    }
    
    &.is-current {
      .point-icon {
        transform: scale(1.2);
        animation: currentPointPulse 2s ease-in-out infinite;
        z-index: 15; // 确保当前位置图标在连接线之上
        position: relative;
      }

      .point-name {
        color: #1890ff;
        font-weight: bold;
        text-shadow: 0 0 5px rgba(24, 144, 255, 0.8);
        animation: textGlow 2s ease-in-out infinite;
      }

      .point-time {
        animation: textGlow 2s ease-in-out infinite;
      }

      // 隐藏当前位置的连接线，避免重复显示
      .point-connector {
        opacity: 0.3; // 降低透明度，减少视觉干扰
      }
    }

    &.is-next {
      .point-icon {
        animation: nextPointBlink 1.5s ease-in-out infinite;
      }

      .point-name {
        color: #faad14;
        font-weight: bold;
        text-shadow: 0 0 5px rgba(250, 173, 20, 0.8);
        animation: nextTextBlink 1.5s ease-in-out infinite;
      }

      .point-time {
        animation: nextTextBlink 1.5s ease-in-out infinite;
      }
    }
    
    .point-icon {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      transition: all 0.3s ease;
      position: relative;
      z-index: 2;
      
      &.icon-checked {
        background: linear-gradient(45deg, #52c41a, #73d13d);
        box-shadow: 0 0 15px rgba(82, 196, 26, 0.6);
        border: 2px solid #ffffff;

        :deep(.anticon) {
          color: #ffffff !important;
          font-size: 16px !important;
        }
      }

      &.icon-current {
        background: linear-gradient(45deg, #1890ff, #40a9ff);
        box-shadow: 0 0 20px rgba(24, 144, 255, 1);
        border: 2px solid #ffffff;

        :deep(.anticon) {
          color: #ffffff !important;
          font-size: 18px !important;
        }
      }

      &.icon-pending {
        background: linear-gradient(45deg, #faad14, #ffc53d);
        border: 1px solid #ffffff;
        box-shadow: 0 0 12px rgba(250, 173, 20, 0.6);

        :deep(.anticon) {
          color: #ffffff !important;
          font-size: 14px !important;
        }
      }

      &.icon-missed {
        background: linear-gradient(45deg, #ff4d4f, #ff7875);
        box-shadow: 0 0 12px rgba(255, 77, 79, 0.6);
        border: 1px solid #ffffff;

        :deep(.anticon) {
          color: #ffffff !important;
          font-size: 16px !important;
        }
      }
    }
    
    .point-info {
      text-align: center;
      min-height: 45px;

      .point-name {
        font-size: 14px;
        color: #ffffff;
        margin-bottom: 5px;
        white-space: nowrap;
      }

      .point-time {
        font-size: 12px;
        color: rgba(0, 255, 255, 0.8);
        font-family: 'Courier New', monospace;
        font-weight: bold;
      }
    }
    
    .point-connector {
      position: absolute;
      top: 18px; // 基于图标中心位置
      left: 50%;
      width: 20px; // 进一步缩短连接线，为箭头留出空间
      height: 3px;
      z-index: 5; // 提高z-index
      transform: translateX(18px); // 从图标右边缘开始
      pointer-events: none; // 避免阻挡点击事件

      .connector-line {
        width: 100%; // 连接线占满整个容器
        height: 100%;
        background: linear-gradient(90deg,
          rgba(0, 255, 255, 0.9) 0%,
          rgba(0, 255, 255, 0.7) 50%,
          rgba(0, 255, 255, 0.9) 100%
        );
        border-radius: 2px;
        position: relative;
        box-shadow: 0 0 6px rgba(0, 255, 255, 0.4);

        // 箭头连接在连接线末端，但不覆盖下一个点
        &::after {
          content: '';
          position: absolute;
          top: 50%;
          right: -8px; // 箭头稍微延伸，但不覆盖下一个点
          width: 0;
          height: 0;
          border-left: 8px solid rgba(0, 255, 255, 0.9);
          border-top: 4px solid transparent;
          border-bottom: 4px solid transparent;
          transform: translateY(-50%);
          filter: drop-shadow(0 0 3px rgba(0, 255, 255, 0.8));
          z-index: 10;
        }
      }
    }

    // 当前位置到下一个点的连接线 - 蓝色到黄色渐变
    &.is-current .point-connector {
      .connector-line {
        background: linear-gradient(90deg,
          rgba(24, 144, 255, 0.9) 0%,   // 蓝色（当前位置）
          rgba(24, 144, 255, 0.7) 30%,
          rgba(250, 173, 20, 0.7) 70%,  // 黄色（下一个目标）
          rgba(250, 173, 20, 0.9) 100%
        );
        box-shadow: 0 0 8px rgba(24, 144, 255, 0.3);

        &::after {
          border-left: 8px solid rgba(250, 173, 20, 0.9); // 箭头使用下一个点的颜色
          filter: drop-shadow(0 0 3px rgba(250, 173, 20, 0.8));
        }
      }
    }

    // 已完成的连接线 - 绿色
    &.status-checked .point-connector {
      .connector-line {
        background: linear-gradient(90deg,
          rgba(82, 196, 26, 0.9) 0%,
          rgba(82, 196, 26, 0.7) 50%,
          rgba(82, 196, 26, 0.9) 100%
        );
        box-shadow: 0 0 6px rgba(82, 196, 26, 0.4);

        &::after {
          border-left: 8px solid rgba(82, 196, 26, 0.9);
          filter: drop-shadow(0 0 3px rgba(82, 196, 26, 0.8));
        }
      }
    }
  }
}

.status-legend {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  
  .legend-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 10px;
    border: 1px solid rgba(0, 255, 255, 0.2);
    
    .point-icon {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
    }
    
    span {
      color: #ffffff;
      font-size: 14px;
    }
  }
}

// 当前位置脉冲动画 - 优化层级和发光效果
@keyframes currentPointPulse {
  0%, 100% {
    transform: scale(1.2);
    box-shadow:
      0 0 20px rgba(24, 144, 255, 0.8),
      0 0 0 0 rgba(24, 144, 255, 0.4);
  }
  50% {
    transform: scale(1.3);
    box-shadow:
      0 0 30px rgba(24, 144, 255, 1),
      0 0 0 10px rgba(24, 144, 255, 0.2);
  }
}

// 下一个目标闪烁动画
@keyframes nextPointBlink {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

// 当前位置文字发光动画
@keyframes textGlow {
  0%, 100% {
    text-shadow: 0 0 5px rgba(24, 144, 255, 0.8);
  }
  50% {
    text-shadow: 0 0 10px rgba(24, 144, 255, 1), 0 0 15px rgba(24, 144, 255, 0.6);
  }
}

// 下一个目标文字闪烁动画
@keyframes nextTextBlink {
  0%, 100% {
    opacity: 1;
    text-shadow: 0 0 5px rgba(250, 173, 20, 0.8);
  }
  50% {
    opacity: 0.8;
    text-shadow: 0 0 8px rgba(250, 173, 20, 1), 0 0 12px rgba(250, 173, 20, 0.6);
  }
}
</style>
