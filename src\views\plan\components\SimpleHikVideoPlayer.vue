<template>
  <j-modal 
    :title="'视频播放'" 
    :width="900" 
    v-model:open="open" 
    :footer="null" 
    @cancel="handleCancel" 
    class="simple-hik-video-modal"
  >
    <div class="p-4">
      <!-- 插件状态提示 -->
      <div v-if="!pluginLoaded" class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div class="flex items-center">
          <Icon icon="ant-design:warning-outlined" class="text-yellow-600 mr-2" />
          <span class="text-yellow-800 text-sm">
            <strong>海康Web插件未加载：</strong>请确保已安装海康Web插件V1.5.5并在浏览器中启用
          </span>
        </div>
        <div class="mt-2">
          <a 
            href="https://www.hikvision.com/cn/support/download/firmware/security-management-software/" 
            target="_blank"
            class="text-blue-600 hover:text-blue-800 text-xs"
          >
            下载海康Web插件
          </a>
        </div>
      </div>

      <!-- 视频播放区域 -->
      <div class="mb-4">
        <div class="relative bg-black rounded-lg overflow-hidden" style="aspect-ratio: 16/9;">
          <!-- 海康插件容器 -->
          <div 
            ref="videoContainer"
            id="hikVideoPlayer"
            class="w-full h-full"
            style="background: #000;"
          >
            <!-- 插件将在这里渲染视频 -->
          </div>
          
          <!-- 加载状态 -->
          <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="text-white text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <div>{{ loadingText }}</div>
            </div>
          </div>
          
          <!-- 无插件提示 -->
          <div v-if="!pluginLoaded" class="absolute inset-0 flex items-center justify-center bg-gray-800">
            <div class="text-center text-white">
              <div class="text-4xl mb-4">🔌</div>
              <div class="text-lg mb-2">海康Web插件未加载</div>
              <div class="text-sm opacity-75">请安装并启用海康Web插件V1.5.5</div>
            </div>
          </div>

          <!-- 播放失败提示 -->
          <div v-if="!loading && !isPlaying && pluginLoaded && hasTriedPlay" class="absolute inset-0 flex items-center justify-center bg-gray-800">
            <div class="text-center text-white">
              <div class="text-4xl mb-4">📹</div>
              <div class="text-lg mb-2">视频播放失败</div>
              <div class="text-sm opacity-75">{{ errorMessage || '请检查视频流地址和网络连接' }}</div>
            </div>
          </div>
        </div>
        
        <!-- 错误信息 -->
        <div v-if="errorMessage" class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-700 text-sm">{{ errorMessage }}</p>
        </div>
        
        <!-- 成功信息 -->
        <div v-if="successMessage" class="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p class="text-green-700 text-sm">{{ successMessage }}</p>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="flex justify-center space-x-3">
        <button 
          v-if="!isPlaying"
          @click="startPlay" 
          :disabled="!pluginLoaded || loading"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors flex items-center"
        >
          <Icon icon="ant-design:play-circle-outlined" class="mr-1" />
          开始播放
        </button>
        <button 
          v-if="isPlaying"
          @click="stopPlay" 
          :disabled="!pluginLoaded"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 transition-colors flex items-center"
        >
          <Icon icon="ant-design:pause-circle-outlined" class="mr-1" />
          停止播放
        </button>
        <button 
          @click="refreshPlugin" 
          :disabled="loading"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors flex items-center"
        >
          <Icon icon="ant-design:reload-outlined" class="mr-1" />
          刷新插件
        </button>
      </div>

      <!-- 视频信息 -->
      <div v-if="rtspUrl" class="mt-4 p-3 bg-gray-50 rounded-lg">
        <div class="text-sm text-gray-600">
          <div class="mb-1"><strong>视频流地址：</strong></div>
          <div class="font-mono text-xs bg-white p-2 rounded border break-all">{{ rtspUrl }}</div>
        </div>
      </div>
    </div>
  </j-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import JModal from "@/components/Modal/src/JModal/JModal.vue";
import { Icon } from '/@/components/Icon';

const open = ref(false);
const videoContainer = ref<HTMLElement>();
const pluginLoaded = ref(false);
const isPlaying = ref(false);
const loading = ref(false);
const loadingText = ref('');
const errorMessage = ref('');
const successMessage = ref('');
const hasTriedPlay = ref(false);

// RTSP视频流信息
const rtspUrl = ref('');

// 海康插件对象
let hikPlugin: any = null;
let previewHandle: number = -1;

// 检测海康插件
const checkHikPlugin = () => {
  try {
    if (window.WebVideoCtrl) {
      pluginLoaded.value = true;
      hikPlugin = window.WebVideoCtrl;
      
      console.log('海康Web插件加载成功');
      
      // 初始化插件
      initPlugin();
    } else {
      pluginLoaded.value = false;
      console.warn('未检测到海康Web插件');
    }
  } catch (error) {
    console.error('检测海康插件失败:', error);
    pluginLoaded.value = false;
  }
};

// 初始化插件
const initPlugin = () => {
  if (!hikPlugin) return;
  
  try {
    // 设置播放容器
    hikPlugin.I_InitPlugin(800, 450, {
      bWndFull: true,
      iWndowType: 1,
      cbSelWnd: function(xmlDoc: any) {
        console.log('窗口选择回调:', xmlDoc);
      }
    });
    
    console.log('插件初始化完成');
  } catch (error) {
    console.error('插件初始化失败:', error);
    errorMessage.value = `插件初始化失败: ${error.message}`;
  }
};

// 解析RTSP URL获取设备信息
const parseRtspUrl = (url: string) => {
  try {
    const urlObj = new URL(url);
    const [username, password] = urlObj.username && urlObj.password 
      ? [urlObj.username, urlObj.password]
      : ['admin', 'admin123']; // 默认值
    
    const ip = urlObj.hostname;
    const port = urlObj.port ? parseInt(urlObj.port) : 554;
    
    // 从路径中提取通道信息（如果有的话）
    const pathMatch = urlObj.pathname.match(/\/(\d+)/);
    const channel = pathMatch ? parseInt(pathMatch[1]) : 1;
    
    return {
      ip,
      port,
      username,
      password,
      channel,
      streamType: 0 // 默认主码流
    };
  } catch (error) {
    console.error('解析RTSP URL失败:', error);
    return null;
  }
};

// 开始播放
const startPlay = async () => {
  if (!hikPlugin || !rtspUrl.value || isPlaying.value) return;
  
  loading.value = true;
  loadingText.value = '正在解析视频流...';
  errorMessage.value = '';
  successMessage.value = '';
  hasTriedPlay.value = true;
  
  try {
    // 解析RTSP URL
    const deviceInfo = parseRtspUrl(rtspUrl.value);
    if (!deviceInfo) {
      throw new Error('无法解析RTSP视频流地址');
    }
    
    console.log('设备信息:', deviceInfo);
    loadingText.value = '正在连接设备...';
    
    // 登录设备
    await new Promise((resolve, reject) => {
      hikPlugin.I_Login(
        deviceInfo.ip,
        1,
        deviceInfo.port,
        deviceInfo.username,
        deviceInfo.password,
        {
          success: function(xmlDoc: any) {
            console.log('设备登录成功:', xmlDoc);
            resolve(xmlDoc);
          },
          error: function(status: any, xmlDoc: any) {
            console.error('设备登录失败:', status, xmlDoc);
            reject(new Error(`设备登录失败: ${status}`));
          }
        }
      );
    });
    
    loadingText.value = '正在启动视频预览...';
    
    // 开始预览
    await new Promise((resolve, reject) => {
      previewHandle = hikPlugin.I_StartRealPlay(deviceInfo.ip, {
        iRtspPort: deviceInfo.port,
        iChannelID: deviceInfo.channel,
        iStreamType: deviceInfo.streamType,
        success: function(xmlDoc: any) {
          console.log('视频预览启动成功:', xmlDoc);
          resolve(xmlDoc);
        },
        error: function(status: any, xmlDoc: any) {
          console.error('视频预览启动失败:', status, xmlDoc);
          reject(new Error(`视频预览启动失败: ${status}`));
        }
      });
    });
    
    isPlaying.value = true;
    successMessage.value = '视频播放成功';
    
  } catch (error) {
    console.error('播放失败:', error);
    errorMessage.value = `播放失败: ${error.message}`;
  } finally {
    loading.value = false;
    loadingText.value = '';
  }
};

// 停止播放
const stopPlay = () => {
  if (!hikPlugin || !isPlaying.value) return;
  
  try {
    if (previewHandle >= 0) {
      hikPlugin.I_Stop(previewHandle);
      previewHandle = -1;
    }
    
    // 登出设备（如果有设备信息的话）
    if (rtspUrl.value) {
      const deviceInfo = parseRtspUrl(rtspUrl.value);
      if (deviceInfo) {
        hikPlugin.I_Logout(deviceInfo.ip);
      }
    }
    
    isPlaying.value = false;
    successMessage.value = '视频播放已停止';
    
  } catch (error) {
    console.error('停止播放失败:', error);
    errorMessage.value = `停止播放失败: ${error.message}`;
  }
};

// 刷新插件
const refreshPlugin = () => {
  // 先停止播放
  if (isPlaying.value) {
    stopPlay();
  }
  
  // 重新检测插件
  setTimeout(() => {
    checkHikPlugin();
  }, 500);
};

// 显示模态框并播放视频
const showModal = (videoUrl: string) => {
  rtspUrl.value = videoUrl;
  open.value = true;
  hasTriedPlay.value = false;
  
  setTimeout(() => {
    checkHikPlugin();
    
    // 如果插件已加载且有视频URL，自动开始播放
    if (pluginLoaded.value && rtspUrl.value) {
      setTimeout(() => {
        startPlay();
      }, 1000);
    }
  }, 100);
};

// 关闭模态框
const handleCancel = () => {
  // 停止播放
  if (isPlaying.value) {
    stopPlay();
  }
  
  open.value = false;
  rtspUrl.value = '';
  errorMessage.value = '';
  successMessage.value = '';
  hasTriedPlay.value = false;
};

// 组件挂载时检测插件
onMounted(() => {
  setTimeout(() => {
    checkHikPlugin();
  }, 1000);
});

// 组件销毁时清理
onBeforeUnmount(() => {
  if (isPlaying.value) {
    stopPlay();
  }
});

defineExpose({
  showModal
});

// 扩展Window接口以支持海康插件
declare global {
  interface Window {
    WebVideoCtrl: any;
  }
}
</script>

<style scoped>
.simple-hik-video-modal :deep(.ant-modal-content) {
  border-radius: 16px;
}

.simple-hik-video-modal :deep(.ant-modal-body) {
  padding: 0;
}

#hikVideoPlayer {
  position: relative;
}
</style>
