<template>
  <j-modal 
    :title="'WebRTC视频播放'" 
    :width="1000" 
    v-model:open="open" 
    :footer="null" 
    @cancel="handleCancel" 
    class="webrtc-video-modal"
  >
    <div class="p-4">
      <!-- RTSP地址输入区域 -->
      <div class="mb-4">
        <h4 class="text-md font-medium mb-3">RTSP视频流测试</h4>
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              RTSP视频流地址：
            </label>
            <div class="flex gap-2">
              <input
                v-model="rtspUrl"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                placeholder="rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream"
              />
              <button
                @click="testConnection"
                :disabled="!rtspUrl || loading"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors text-sm"
              >
                测试连接
              </button>
            </div>
          </div>

          <!-- 预设RTSP地址 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              预设地址（点击使用）：
            </label>
            <div class="grid grid-cols-1 gap-2">
              <button
                v-for="(preset, index) in presetUrls"
                :key="index"
                @click="usePresetUrl(preset.url)"
                class="text-left p-2 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
              >
                <div class="font-medium text-sm text-gray-800">{{ preset.name }}</div>
                <div class="text-xs text-gray-500 font-mono">{{ preset.url }}</div>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频播放区域 -->
      <div class="mb-4">
        <div class="relative bg-black rounded-lg overflow-hidden" style="aspect-ratio: 16/9;">
          <!-- WebRTC视频元素 -->
          <video 
            ref="videoElement"
            class="w-full h-full object-cover"
            autoplay
            muted
            playsinline
            controls
          >
            您的浏览器不支持视频播放
          </video>
          
          <!-- 加载状态 -->
          <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="text-white text-center">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <div>{{ loadingText }}</div>
            </div>
          </div>
          
          <!-- 连接状态指示器 -->
          <div class="absolute top-4 right-4 flex items-center space-x-2">
            <div class="flex items-center bg-black bg-opacity-50 rounded-full px-3 py-1">
              <div 
                :class="[
                  'w-2 h-2 rounded-full mr-2',
                  connectionState === 'connected' ? 'bg-green-500' : 
                  connectionState === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
                ]"
              ></div>
              <span class="text-white text-xs">{{ getConnectionStateText() }}</span>
            </div>
          </div>
          
          <!-- 播放失败提示 -->
          <div v-if="!loading && !isPlaying && hasTriedPlay" class="absolute inset-0 flex items-center justify-center bg-gray-800">
            <div class="text-center text-white">
              <div class="text-4xl mb-4">📹</div>
              <div class="text-lg mb-2">视频播放失败</div>
              <div class="text-sm opacity-75">{{ errorMessage || '请检查视频流地址和网络连接' }}</div>
              <button 
                @click="retryConnection" 
                class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                重试连接
              </button>
            </div>
          </div>
        </div>
        
        <!-- 错误信息 -->
        <div v-if="errorMessage" class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-700 text-sm">{{ errorMessage }}</p>
        </div>
        
        <!-- 成功信息 -->
        <div v-if="successMessage" class="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p class="text-green-700 text-sm">{{ successMessage }}</p>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="flex justify-center space-x-3 mb-4">
        <button 
          v-if="!isPlaying"
          @click="startPlay" 
          :disabled="loading || !rtspUrl"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors flex items-center"
        >
          <Icon icon="ant-design:play-circle-outlined" class="mr-1" />
          开始播放
        </button>
        <button 
          v-if="isPlaying"
          @click="stopPlay" 
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
        >
          <Icon icon="ant-design:pause-circle-outlined" class="mr-1" />
          停止播放
        </button>
        <button 
          @click="captureFrame" 
          :disabled="!isPlaying"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors flex items-center"
        >
          <Icon icon="ant-design:camera-outlined" class="mr-1" />
          截图
        </button>
        <button 
          @click="toggleFullscreen" 
          class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors flex items-center"
        >
          <Icon icon="ant-design:fullscreen-outlined" class="mr-1" />
          全屏
        </button>
      </div>

      <!-- 视频信息 -->
      <div v-if="rtspUrl" class="p-3 bg-gray-50 rounded-lg">
        <div class="text-sm text-gray-600 space-y-2">
          <div>
            <strong>视频流地址：</strong>
            <div class="font-mono text-xs bg-white p-2 rounded border break-all mt-1">{{ rtspUrl }}</div>
          </div>
          <div class="grid grid-cols-2 gap-4 mt-2">
            <div><strong>连接状态：</strong> {{ getConnectionStateText() }}</div>
            <div><strong>播放状态：</strong> {{ isPlaying ? '播放中' : '已停止' }}</div>
          </div>
        </div>
      </div>

      <!-- WebRTC统计信息 -->
      <div v-if="isPlaying && stats" class="mt-4 p-3 bg-blue-50 rounded-lg">
        <div class="text-sm text-blue-800">
          <div class="font-medium mb-2">WebRTC统计信息：</div>
          <div class="grid grid-cols-2 gap-2 text-xs">
            <div>视频编码：{{ stats.videoCodec || 'N/A' }}</div>
            <div>分辨率：{{ stats.videoResolution || 'N/A' }}</div>
            <div>帧率：{{ stats.frameRate || 'N/A' }} fps</div>
            <div>比特率：{{ stats.bitrate || 'N/A' }} kbps</div>
          </div>
        </div>
      </div>
    </div>
  </j-modal>
</template>

<script setup lang="ts">
import { ref, onBeforeUnmount } from 'vue';

interface VideoStats {
  videoCodec?: string;
  videoResolution?: string;
  frameRate?: number;
  bitrate?: number;
}

const open = ref(false);
const videoElement = ref<HTMLVideoElement | null>(null);
const isPlaying = ref(false);
const loading = ref(false);
const loadingText = ref('');
const errorMessage = ref('');
const successMessage = ref('');
const hasTriedPlay = ref(false);
const connectionState = ref<'disconnected' | 'connecting' | 'connected' | 'failed'>('disconnected');
const stats = ref<VideoStats>({});

// RTSP视频流信息
const rtspUrl = ref('rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream');

// 预设RTSP地址
const presetUrls = ref([
  {
    name: '海康威视标准格式',
    url: 'rtsp://admin:admin123@*************:554/Streaming/Channels/101'
  },
  {
    name: '海康威视主码流',
    url: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream'
  },
  {
    name: '海康威视子码流',
    url: 'rtsp://admin:admin123@*************:554/h264/ch1/sub/av_stream'
  },
  {
    name: '大华标准格式',
    url: 'rtsp://admin:admin123@*************:554/cam/realmonitor?channel=1&subtype=0'
  },
  {
    name: '通用RTSP格式',
    url: 'rtsp://admin:admin123@*************:554/live/ch1'
  },
  {
    name: '自定义端口示例',
    url: 'rtsp://user:pass@10.0.0.100:8554/stream1'
  }
]);

// WebRTC相关对象
let peerConnection: RTCPeerConnection | null = null;
let websocket: WebSocket | null = null;
let statsInterval: number | null = null;

// WebRTC服务器配置
const webrtcConfig = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { urls: 'stun:stun1.l.google.com:19302' }
  ]
};

// 获取连接状态文本
const getConnectionStateText = () => {
  switch (connectionState.value) {
    case 'connected': return '已连接';
    case 'connecting': return '连接中';
    case 'failed': return '连接失败';
    default: return '未连接';
  }
};

// 使用预设URL
const usePresetUrl = (url: string) => {
  rtspUrl.value = url;
  successMessage.value = `已选择预设地址: ${url}`;
  errorMessage.value = '';
};

// 测试连接
const testConnection = async () => {
  if (!rtspUrl.value) {
    errorMessage.value = '请输入RTSP地址';
    return;
  }

  // 验证RTSP URL格式
  if (!rtspUrl.value.startsWith('rtsp://')) {
    errorMessage.value = 'RTSP地址必须以 rtsp:// 开头';
    return;
  }

  try {
    // 解析URL检查格式
    const url = new URL(rtspUrl.value);
    successMessage.value = `RTSP地址格式正确: ${url.hostname}:${url.port || 554}`;
    errorMessage.value = '';

    // 自动开始播放测试
    setTimeout(() => {
      startPlay();
    }, 1000);

  } catch (error) {
    errorMessage.value = `RTSP地址格式错误: ${error.message}`;
  }
};

// 初始化WebRTC连接
const initWebRTC = async () => {
  try {
    // 创建RTCPeerConnection
    peerConnection = new RTCPeerConnection(webrtcConfig);
    
    // 监听连接状态变化
    peerConnection.onconnectionstatechange = () => {
      if (peerConnection) {
        connectionState.value = peerConnection.connectionState as any;
        console.log('WebRTC连接状态:', peerConnection.connectionState);
        
        if (peerConnection.connectionState === 'failed') {
          errorMessage.value = 'WebRTC连接失败，请检查网络连接';
          stopPlay();
        }
      }
    };
    
    // 监听ICE连接状态变化
    peerConnection.oniceconnectionstatechange = () => {
      if (peerConnection) {
        console.log('ICE连接状态:', peerConnection.iceConnectionState);
      }
    };
    
    // 监听远程流
    peerConnection.ontrack = (event) => {
      console.log('收到远程视频流:', event);
      if (videoElement.value && event.streams[0]) {
        videoElement.value.srcObject = event.streams[0];
        isPlaying.value = true;
        successMessage.value = 'WebRTC视频流连接成功';
        
        // 开始统计信息收集
        startStatsCollection();
      }
    };
    
    // 监听ICE候选
    peerConnection.onicecandidate = (event) => {
      if (event.candidate && websocket && websocket.readyState === WebSocket.OPEN) {
        websocket.send(JSON.stringify({
          type: 'ice-candidate',
          candidate: event.candidate
        }));
      }
    };
    
    console.log('WebRTC初始化完成');
    
  } catch (error) {
    console.error('WebRTC初始化失败:', error);
    errorMessage.value = `WebRTC初始化失败: ${error.message}`;
    throw error;
  }
};

// 连接WebSocket信令服务器
const connectWebSocket = async () => {
  return new Promise((resolve, reject) => {
    try {
      // 这里需要替换为实际的WebRTC信令服务器地址
      const wsUrl = `ws://localhost:8080/webrtc/signal?rtsp=${encodeURIComponent(rtspUrl.value)}`;
      websocket = new WebSocket(wsUrl);
      
      websocket.onopen = () => {
        console.log('WebSocket连接成功');
        connectionState.value = 'connecting';
        resolve(true);
      };
      
      websocket.onmessage = async (event) => {
        try {
          const message = JSON.parse(event.data);
          await handleSignalingMessage(message);
        } catch (error) {
          console.error('处理信令消息失败:', error);
        }
      };
      
      websocket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        errorMessage.value = 'WebSocket连接失败，请检查信令服务器';
        reject(error);
      };
      
      websocket.onclose = () => {
        console.log('WebSocket连接关闭');
        connectionState.value = 'disconnected';
      };
      
    } catch (error) {
      console.error('WebSocket连接失败:', error);
      reject(error);
    }
  });
};

// 处理信令消息
const handleSignalingMessage = async (message: any) => {
  if (!peerConnection) return;
  
  switch (message.type) {
    case 'offer':
      await peerConnection.setRemoteDescription(new RTCSessionDescription(message.offer));
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);
      
      if (websocket && websocket.readyState === WebSocket.OPEN) {
        websocket.send(JSON.stringify({
          type: 'answer',
          answer: answer
        }));
      }
      break;
      
    case 'ice-candidate':
      await peerConnection.addIceCandidate(new RTCIceCandidate(message.candidate));
      break;
      
    case 'error':
      errorMessage.value = `服务器错误: ${message.message}`;
      stopPlay();
      break;
      
    default:
      console.log('未知信令消息:', message);
  }
};

// 开始播放
const startPlay = async () => {
  if (!rtspUrl.value || isPlaying.value) return;
  
  loading.value = true;
  loadingText.value = '正在初始化WebRTC...';
  errorMessage.value = '';
  successMessage.value = '';
  hasTriedPlay.value = true;
  
  try {
    // 1. 初始化WebRTC
    await initWebRTC();
    
    loadingText.value = '正在连接信令服务器...';
    
    // 2. 连接WebSocket信令服务器
    await connectWebSocket();
    
    loadingText.value = '正在建立视频连接...';
    
    // 3. 发送播放请求
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        type: 'play',
        rtspUrl: rtspUrl.value
      }));
    }
    
    connectionState.value = 'connecting';
    
  } catch (error) {
    console.error('播放失败:', error);
    errorMessage.value = `播放失败: ${error.message}`;
    stopPlay();
  } finally {
    loading.value = false;
    loadingText.value = '';
  }
};

// 停止播放
const stopPlay = () => {
  try {
    // 停止统计信息收集
    if (statsInterval) {
      clearInterval(statsInterval);
      statsInterval = null;
    }
    
    // 关闭WebRTC连接
    if (peerConnection) {
      peerConnection.close();
      peerConnection = null;
    }
    
    // 关闭WebSocket连接
    if (websocket) {
      websocket.close();
      websocket = null;
    }
    
    // 清理视频元素
    if (videoElement.value) {
      videoElement.value.srcObject = null;
    }
    
    isPlaying.value = false;
    connectionState.value = 'disconnected';
    successMessage.value = '视频播放已停止';
    stats.value = {};
    
  } catch (error) {
    console.error('停止播放失败:', error);
    errorMessage.value = `停止播放失败: ${error.message}`;
  }
};

// 重试连接
const retryConnection = () => {
  stopPlay();
  setTimeout(() => {
    startPlay();
  }, 1000);
};

// 截图
const captureFrame = () => {
  if (!videoElement.value || !isPlaying.value) return;
  
  try {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    canvas.width = videoElement.value.videoWidth;
    canvas.height = videoElement.value.videoHeight;
    
    if (ctx) {
      ctx.drawImage(videoElement.value, 0, 0);
      
      // 下载截图
      const link = document.createElement('a');
      link.download = `screenshot_${new Date().getTime()}.png`;
      link.href = canvas.toDataURL();
      link.click();
      
      successMessage.value = '截图成功';
    }
  } catch (error) {
    console.error('截图失败:', error);
    errorMessage.value = `截图失败: ${error.message}`;
  }
};

// 全屏切换
const toggleFullscreen = () => {
  if (!videoElement.value) return;
  
  if (document.fullscreenElement) {
    document.exitFullscreen();
  } else {
    videoElement.value.requestFullscreen();
  }
};

// 开始统计信息收集
const startStatsCollection = () => {
  if (!peerConnection) return;
  
  statsInterval = setInterval(async () => {
    try {
      const statsReport = await peerConnection!.getStats();
      const newStats: VideoStats = {};
      
      statsReport.forEach((report) => {
        if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
          newStats.frameRate = report.framesPerSecond;
          newStats.bitrate = Math.round(report.bytesReceived * 8 / 1000); // kbps
        }
        
        if (report.type === 'track' && report.kind === 'video') {
          newStats.videoResolution = `${report.frameWidth}x${report.frameHeight}`;
        }
        
        if (report.type === 'codec' && report.mimeType?.includes('video')) {
          newStats.videoCodec = report.mimeType.split('/')[1];
        }
      });
      
      stats.value = newStats;
      
    } catch (error) {
      console.error('获取统计信息失败:', error);
    }
  }, 1000);
};

// 显示模态框并播放视频
const showModal = (videoUrl: string) => {
  rtspUrl.value = videoUrl;
  open.value = true;
  hasTriedPlay.value = false;
  
  // 自动开始播放
  setTimeout(() => {
    startPlay();
  }, 500);
};

// 关闭模态框
const handleCancel = () => {
  stopPlay();
  open.value = false;
  rtspUrl.value = '';
  errorMessage.value = '';
  successMessage.value = '';
  hasTriedPlay.value = false;
};

// 组件销毁时清理
onBeforeUnmount(() => {
  stopPlay();
});

defineExpose({
  showModal
});
</script>

<style scoped>
.webrtc-video-modal :deep(.ant-modal-content) {
  border-radius: 16px;
}

.webrtc-video-modal :deep(.ant-modal-body) {
  padding: 0;
}

video {
  background: #000;
}
</style>
