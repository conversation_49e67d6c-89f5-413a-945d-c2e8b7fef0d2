<template>
  <div class="video-monitor-grid">
    <!-- 工具栏 -->
    <div class="monitor-toolbar">
      <div class="toolbar-left">
        <h3>视频监控 ({{ videoList.length }})</h3>
        <a-tag color="blue">{{ planInfo?.name || '巡更计划' }}</a-tag>
      </div>
      <div class="toolbar-right">
        <a-button-group size="small">
          <a-button @click="setGridLayout(1)" :type="gridLayout === 1 ? 'primary' : 'default'">
            <AppstoreOutlined /> 1x1
          </a-button>
          <a-button @click="setGridLayout(4)" :type="gridLayout === 4 ? 'primary' : 'default'">
            <BorderOutlined /> 2x2
          </a-button>
          <a-button @click="setGridLayout(9)" :type="gridLayout === 9 ? 'primary' : 'default'">
            <BorderlessTableOutlined /> 3x3
          </a-button>
        </a-button-group>
        
        <a-divider type="vertical" />
        
        <a-button @click="startAllVideos" :loading="startingAll" size="small">
          <PlayCircleOutlined /> 全部播放
        </a-button>
        <a-button @click="stopAllVideos" size="small">
          <PauseCircleOutlined /> 全部停止
        </a-button>
        <a-button @click="refreshAllVideos" size="small">
          <ReloadOutlined /> 全部刷新
        </a-button>
        
        <a-divider type="vertical" />
        
        <a-button @click="captureAllFrames" size="small">
          <CameraOutlined /> 全部截图
        </a-button>
        <a-button @click="exportVideoList" size="small">
          <ExportOutlined /> 导出列表
        </a-button>
      </div>
    </div>
    
    <!-- 视频网格 -->
    <div class="video-grid" :class="`grid-${getGridClass()}`">
      <div
        v-for="(video, index) in displayVideoList"
        :key="video.id || index"
        class="video-grid-item"
      >
        <VideoMonitorPlayer
          v-if="video.id"
          :ref="el => setVideoPlayerRef(el, index)"
          :plan-id="planInfo?.id || ''"
          :video-info="video"
          :auto-start="autoStartVideos"
          :show-controls="true"
          @error="onVideoError(index, $event)"
        />
        <div v-else class="empty-video-slot">
          <div class="empty-content">
            <VideoCameraOutlined />
            <p>空闲位置</p>
          </div>
        </div>
      </div>
    </div>
    

    
    <!-- 批量操作进度 -->
    <a-modal
      v-model:open="batchProgressVisible"
      title="批量操作进度"
      :footer="null"
      :closable="false"
    >
      <div class="batch-progress">
        <a-progress 
          :percent="batchProgress" 
          :status="batchProgressStatus"
          :stroke-color="batchProgressStatus === 'exception' ? '#ff4d4f' : '#1890ff'"
        />
        <p>{{ batchProgressText }}</p>
        <div class="progress-details">
          <span>成功: {{ batchSuccessCount }}</span>
          <span>失败: {{ batchFailCount }}</span>
          <span>总计: {{ videoList.length }}</span>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, onUnmounted, nextTick } from 'vue';
import { message } from 'ant-design-vue';
import {
  AppstoreOutlined,
  BorderOutlined,
  BorderlessTableOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  ReloadOutlined,
  CameraOutlined,
  ExportOutlined,
  VideoCameraOutlined
} from '@ant-design/icons-vue';
import VideoMonitorPlayer from './VideoMonitorPlayer.vue';

interface VideoInfo {
  id: string;
  name: string;
  videoUrl: string;
  streamId: string;
  websocketUrl: string;
  hlsUrl: string;
  streamType?: string;
}

interface PlanInfo {
  id: string;
  name: string;
}

interface Props {
  planInfo: PlanInfo;
  videoList: VideoInfo[];
  autoStartVideos?: boolean;
  defaultGridLayout?: number;
}

const props = withDefaults(defineProps<Props>(), {
  autoStartVideos: false,
  defaultGridLayout: 4
});

// 响应式数据
const gridLayout = ref(props.defaultGridLayout);
const startingAll = ref(false);
const batchProgressVisible = ref(false);
const batchProgress = ref(0);
const batchProgressStatus = ref<'normal' | 'exception' | 'success'>('normal');
const batchProgressText = ref('');
const batchSuccessCount = ref(0);
const batchFailCount = ref(0);

// 视频播放器引用
const videoPlayerRefs = ref<any[]>([]);

// 计算属性
const displayVideoList = computed(() => {
  const list = [...props.videoList];
  const totalSlots = gridLayout.value;
  
  // 填充空位置
  while (list.length < totalSlots) {
    list.push({} as VideoInfo);
  }
  
  return list.slice(0, totalSlots);
});



// 组件卸载
onUnmounted(() => {
  stopAllVideos();
});

/**
 * 设置视频播放器引用
 */
function setVideoPlayerRef(el: any, index: number) {
  if (el) {
    videoPlayerRefs.value[index] = el;
  }
}

/**
 * 设置网格布局
 */
function setGridLayout(layout: number) {
  gridLayout.value = layout;
}

/**
 * 获取网格CSS类名
 */
function getGridClass() {
  switch (gridLayout.value) {
    case 1: return '1x1';
    case 4: return '2x2';
    case 9: return '3x3';
    default: return '2x2';
  }
}

/**
 * 开始所有视频
 */
async function startAllVideos() {
  if (props.videoList.length === 0) {
    message.warning('没有可播放的视频');
    return;
  }
  
  startingAll.value = true;
  batchProgressVisible.value = true;
  batchProgress.value = 0;
  batchProgressStatus.value = 'normal';
  batchSuccessCount.value = 0;
  batchFailCount.value = 0;
  
  try {
    for (let i = 0; i < props.videoList.length; i++) {
      batchProgressText.value = `正在启动视频 ${i + 1}/${props.videoList.length}`;
      
      try {
        const player = videoPlayerRefs.value[i];
        if (player && player.startVideo) {
          await player.startVideo();
          batchSuccessCount.value++;
        }
      } catch (error) {
        console.error(`启动视频 ${i + 1} 失败:`, error);
        batchFailCount.value++;
      }
      
      batchProgress.value = Math.round(((i + 1) / props.videoList.length) * 100);
      
      // 添加延迟避免同时启动太多流
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    batchProgressStatus.value = batchFailCount.value > 0 ? 'exception' : 'success';
    batchProgressText.value = `完成! 成功: ${batchSuccessCount.value}, 失败: ${batchFailCount.value}`;
    
    setTimeout(() => {
      batchProgressVisible.value = false;
    }, 2000);
    
  } finally {
    startingAll.value = false;
  }
}

/**
 * 停止所有视频
 */
function stopAllVideos() {
  console.log('VideoMonitorGrid 停止所有视频，播放器数量:', videoPlayerRefs.value.length);

  videoPlayerRefs.value.forEach((player, index) => {
    if (player) {
      console.log(`停止视频播放器 ${index}`);

      // 先停止视频
      if (typeof player.stopVideo === 'function') {
        try {
          player.stopVideo();
        } catch (error) {
          console.error(`停止视频播放器 ${index} 时出错:`, error);
        }
      }

      // 然后清理资源
      if (typeof player.cleanup === 'function') {
        try {
          player.cleanup();
        } catch (error) {
          console.error(`清理视频播放器 ${index} 资源时出错:`, error);
        }
      }
    }
  });

  console.log('VideoMonitorGrid 所有视频已停止');
  message.info('已停止所有视频');
}

/**
 * 刷新所有视频
 */
function refreshAllVideos() {
  videoPlayerRefs.value.forEach(player => {
    if (player && player.refreshVideo) {
      player.refreshVideo();
    }
  });
  message.info('已刷新所有视频');
}

/**
 * 截图所有视频
 */
function captureAllFrames() {
  let captureCount = 0;
  
  videoPlayerRefs.value.forEach(player => {
    if (player && player.captureFrame && player.isPlaying()) {
      player.captureFrame();
      captureCount++;
    }
  });
  
  if (captureCount > 0) {
    message.success(`已截图 ${captureCount} 个视频`);
  } else {
    message.warning('没有正在播放的视频可以截图');
  }
}

/**
 * 导出视频列表
 */
function exportVideoList() {
  const data = props.videoList.map(video => ({
    名称: video.name,
    类型: getStreamTypeText(video.streamType),
    RTSP地址: video.videoUrl,
    流ID: video.streamId
  }));
  
  const csv = convertToCSV(data);
  downloadCSV(csv, `视频列表_${props.planInfo?.name || '巡更计划'}_${new Date().toISOString().slice(0, 10)}.csv`);
}



/**
 * 获取流类型颜色
 */
function getStreamTypeColor(type?: string) {
  switch (type) {
    case 'playback': return 'blue';
    case 'preview': return 'green';
    default: return 'default';
  }
}

/**
 * 获取流类型文本
 */
function getStreamTypeText(type?: string) {
  switch (type) {
    case 'playback': return '回放流';
    case 'preview': return '预览流';
    default: return '未知';
  }
}

/**
 * 视频错误处理
 */
function onVideoError(index: number, error: any) {
  console.error(`视频 ${index + 1} 错误:`, error);
  message.error(`视频 ${props.videoList[index]?.name || index + 1} 播放错误`);
}

/**
 * 转换为CSV
 */
function convertToCSV(data: any[]) {
  if (data.length === 0) return '';
  
  const headers = Object.keys(data[0]);
  const csvContent = [
    headers.join(','),
    ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
  ].join('\n');
  
  return csvContent;
}

/**
 * 下载CSV文件
 */
function downloadCSV(content: string, filename: string) {
  const blob = new Blob(['\ufeff' + content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  link.click();
}


</script>

<style scoped>
.video-monitor-grid {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.monitor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  flex-shrink: 0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-left h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.video-grid {
  flex: 1;
  display: grid;
  gap: 8px;
  padding: 8px;
  background: #f0f0f0;
  overflow: auto;
}

.video-grid.grid-1x1 {
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
}

.video-grid.grid-2x2 {
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
}

.video-grid.grid-3x3 {
  grid-template-columns: 1fr 1fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
}

.video-grid-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.video-grid-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-grid-item.active {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.empty-video-slot {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
}

.empty-content {
  text-align: center;
  color: #999;
}

.empty-content .anticon {
  font-size: 32px;
  margin-bottom: 8px;
}

.video-detail {
  padding: 16px 0;
}

.video-actions {
  margin-top: 16px;
}

.batch-progress {
  text-align: center;
}

.progress-details {
  display: flex;
  justify-content: space-around;
  margin-top: 12px;
  font-size: 12px;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monitor-toolbar {
    flex-direction: column;
    gap: 12px;
    padding: 12px;
  }
  
  .toolbar-right {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .video-grid.grid-3x3 {
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr 1fr;
  }
}

@media (max-width: 480px) {
  .video-grid.grid-2x2,
  .video-grid.grid-3x3 {
    grid-template-columns: 1fr;
  }
}
</style>
