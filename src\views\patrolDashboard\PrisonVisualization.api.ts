import { defHttp } from '/@/utils/http/axios';

enum Api {
  getPrisonVisualizationData = '/plan/plan/prison',
  updatePatrolStatus = '/patrol/visualization/updateStatus',
}

/**
 * 获取监区可视化数据
 * @param params
 */
export const getPrisonVisualizationData = (params?: any) => {
  return defHttp.get({ 
    url: Api.getPrisonVisualizationData, 
    params: params || {} 
  });
};

/**
 * 更新巡更状态
 * @param params
 */
export const updatePatrolStatus = (params: any) => {
  return defHttp.post({ 
    url: Api.updatePatrolStatus, 
    params 
  });
};

// 人员信息接口
export interface StaffInfo {
  id: string | number;
  name: string;
  cardId: string;
  avatar?: string;
  status: 'on-duty' | 'online' | 'offline';
  isOnDuty: boolean;
  department: string;
  position: string;
}

// 巡更点接口
export interface PatrolPoint {
  id: string | number;
  name: string;
  status: 'checked' | 'current' | 'pending' | 'missed';
  checkTime?: string;
  location?: {
    x: number;
    y: number;
  };
  description?: string;
}

// 巡更计划接口
export interface PatrolPlan {
  id: string | number;
  name?: string;
  sectionName: string;
  status: 'in-progress' | 'pending' | 'completed' | 'missed';
  startTime: string;
  endTime: string;
  estimatedDuration: string;
  currentDuration?: string;
  actualDuration?: string;
  staff: StaffInfo;
  points: PatrolPoint[];
  priority?: 'high' | 'medium' | 'low';
  notes?: string;
}

// 分监区接口
export interface PrisonSection {
  id: string | number;
  name: string;
  staff: StaffInfo[];
  patrolPlans: PatrolPlan[];
}

// 监区可视化数据接口
export interface PrisonVisualizationData {
  prisonName: string;
  sections: PrisonSection[];
  totalStatistics: {
    totalStaff: number;
    onDutyStaff: number;
    totalPlans: number;
    inProgressPlans: number;
    completedPlans: number;
    missedPlans: number;
  };
}

// 生成人员数据
const generateStaff = (sectionName: string, startId: number): StaffInfo[] => [
  {
    id: startId,
    name: `${sectionName.slice(0, 3)}张三`,
    cardId: `P${String(startId).padStart(3, '0')}`,
    avatar: '',
    status: 'on-duty',
    isOnDuty: true,
    department: sectionName,
    position: '巡更员'
  },
  {
    id: startId + 1,
    name: `${sectionName.slice(0, 3)}李四`,
    cardId: `P${String(startId + 1).padStart(3, '0')}`,
    avatar: '',
    status: 'online',
    isOnDuty: false,
    department: sectionName,
    position: '巡更员'
  },
  {
    id: startId + 2,
    name: `${sectionName.slice(0, 3)}王五`,
    cardId: `P${String(startId + 2).padStart(3, '0')}`,
    avatar: '',
    status: 'offline',
    isOnDuty: false,
    department: sectionName,
    position: '巡更员'
  }
];

// 生成巡更计划数据
const generatePatrolPlans = (sectionName: string, staff: StaffInfo[], startId: number): PatrolPlan[] => {
  const plans: PatrolPlan[] = [];
  const statuses: ('in-progress' | 'pending' | 'completed' | 'missed')[] = ['in-progress', 'pending', 'completed'];
  
  for (let i = 0; i < 3; i++) {
    const planId = startId + i;
    const status = statuses[i % statuses.length];
    const staffMember = staff[i % staff.length];
    
    // 生成12个巡更点
    const points: PatrolPoint[] = [
      { id: planId * 100 + 1, name: '活动室', status: status === 'completed' ? 'checked' : (status === 'in-progress' && i === 0 ? 'checked' : 'pending') },
      { id: planId * 100 + 2, name: '1号室', status: status === 'completed' ? 'checked' : (status === 'in-progress' && i === 0 ? 'checked' : 'pending') },
      { id: planId * 100 + 3, name: '2号室', status: status === 'completed' ? 'checked' : (status === 'in-progress' && i === 0 ? 'checked' : 'pending') },
      { id: planId * 100 + 4, name: '3号室', status: status === 'completed' ? 'checked' : (status === 'in-progress' && i === 0 ? 'current' : 'pending') },
      { id: planId * 100 + 5, name: '4号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 6, name: '5号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 7, name: '6号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 8, name: '7号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 9, name: '8号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 10, name: '9号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 11, name: '10号室', status: status === 'completed' ? 'checked' : 'pending' },
      { id: planId * 100 + 12, name: '活动室', status: status === 'completed' ? 'checked' : 'pending' }
    ];
    
    plans.push({
      id: planId,
      name: `${sectionName}${['上午', '下午', '夜间'][i]}巡更`,
      sectionName,
      status,
      startTime: ['08:00', '14:00', '20:00'][i],
      endTime: ['10:00', '16:00', '22:00'][i],
      estimatedDuration: '2小时',
      currentDuration: status === 'in-progress' ? '45分钟' : undefined,
      actualDuration: status === 'completed' ? '1小时50分钟' : undefined,
      staff: staffMember,
      points,
      priority: 'high'
    });
  }
  
  return plans;
};

// 生成监区可视化模拟数据
export const generatePrisonVisualizationMockData = (): PrisonVisualizationData => {
  // 生成三个分监区数据
  const sections: PrisonSection[] = [
    {
      id: 1,
      name: '第一分监区',
      staff: generateStaff('第一分监区', 1),
      patrolPlans: []
    },
    {
      id: 2,
      name: '第二分监区',
      staff: generateStaff('第二分监区', 4),
      patrolPlans: []
    },
    {
      id: 3,
      name: '第三分监区',
      staff: generateStaff('第三分监区', 7),
      patrolPlans: []
    }
  ];

  // 为每个分监区生成巡更计划
  sections.forEach((section, index) => {
    section.patrolPlans = generatePatrolPlans(section.name, section.staff, (index + 1) * 10);
  });

  return {
    prisonName: '某某监狱',
    sections,
    totalStatistics: {
      totalStaff: 9,
      onDutyStaff: 3,
      totalPlans: 9,
      inProgressPlans: 3,
      completedPlans: 3,
      missedPlans: 0
    }
  };
};
