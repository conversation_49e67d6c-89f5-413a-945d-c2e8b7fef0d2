<template>
  <BasicModal 
    title="RTSP视频流测试" 
    :width="800" 
    v-model:open="open" 
    @cancel="handleCancel"
  >
    <div class="p-4">
      <h3 class="text-lg font-medium mb-4">🎥 RTSP视频流测试播放器</h3>
      
      <!-- RTSP地址输入 -->
      <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
          RTSP视频流地址：
        </label>
        <div class="flex gap-2">
          <input 
            v-model="rtspUrl" 
            type="text" 
            class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
            placeholder="rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream"
          />
          <button 
            @click="testRtsp" 
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
          >
            测试
          </button>
        </div>
      </div>

      <!-- 视频播放区域 -->
      <div class="mb-4">
        <div class="bg-black rounded-lg overflow-hidden" style="aspect-ratio: 16/9;">
          <video 
            ref="videoElement"
            id="rtspVideo" 
            autoplay 
            muted
            playsinline
            controls
            class="w-full h-full object-cover"
            style="display: none;"
          >
            您的浏览器不支持视频播放
          </video>
          
          <div v-if="!isPlaying" class="w-full h-full flex items-center justify-center text-white">
            <div class="text-center">
              <div class="text-4xl mb-4">🎥</div>
              <div class="text-lg mb-2">RTSP视频流播放器</div>
              <div class="text-sm opacity-75">输入RTSP地址并点击播放</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="flex justify-center space-x-3 mb-4">
        <button 
          v-if="!isPlaying"
          @click="startPlay" 
          :disabled="!rtspUrl"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors"
        >
          ▶️ 开始播放
        </button>
        <button 
          v-if="isPlaying"
          @click="stopPlay" 
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          ⏹️ 停止播放
        </button>
      </div>

      <!-- 状态信息 -->
      <div v-if="statusMessage" class="p-3 rounded-lg text-sm" :class="statusClass">
        {{ statusMessage }}
      </div>

      <!-- 使用说明 -->
      <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div class="text-sm text-yellow-800">
          <div class="font-medium mb-1">📖 使用说明：</div>
          <ul class="text-xs space-y-1">
            <li>• 需要启动WebRTC-Streamer服务器（端口8000）</li>
            <li>• 输入有效的RTSP地址</li>
            <li>• 点击"开始播放"观看视频</li>
          </ul>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { BasicModal } from '/@/components/Modal';

const open = ref(false);
const rtspUrl = ref('rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream');
const isPlaying = ref(false);
const statusMessage = ref('');
const statusClass = ref('');
const videoElement = ref<HTMLVideoElement | null>(null);

// WebRTC-Streamer实例
let webRtcServer: any = null;

// 测试RTSP地址
const testRtsp = () => {
  if (!rtspUrl.value) {
    setStatus('请输入RTSP地址', 'error');
    return;
  }
  
  if (!rtspUrl.value.startsWith('rtsp://')) {
    setStatus('RTSP地址必须以 rtsp:// 开头', 'error');
    return;
  }
  
  setStatus('RTSP地址格式正确', 'success');
};

// 开始播放
const startPlay = () => {
  if (!rtspUrl.value) {
    setStatus('请输入RTSP地址', 'error');
    return;
  }

  try {
    // 检查WebRtcStreamer是否可用
    if (typeof (window as any).WebRtcStreamer === 'undefined') {
      setStatus('WebRTC-Streamer库未加载，请检查服务器是否启动', 'error');
      return;
    }

    // 初始化WebRTC-Streamer
    if (!webRtcServer) {
      const serverUrl = location.protocol + '//127.0.0.1:8000';
      webRtcServer = new (window as any).WebRtcStreamer('rtspVideo', serverUrl);
    }

    // 连接RTSP流
    webRtcServer.connect(rtspUrl.value);

    // 显示视频元素
    if (videoElement.value) {
      videoElement.value.style.display = 'block';
    }

    isPlaying.value = true;
    setStatus('正在连接RTSP流...', 'info');

    // 监听视频加载事件
    if (videoElement.value) {
      videoElement.value.addEventListener('loadeddata', () => {
        setStatus('RTSP视频流播放成功', 'success');
      }, { once: true });

      videoElement.value.addEventListener('error', () => {
        setStatus('视频播放失败，请检查RTSP地址和网络连接', 'error');
        stopPlay();
      }, { once: true });
    }

  } catch (error) {
    console.error('播放失败:', error);
    setStatus(`播放失败: ${(error as Error).message}`, 'error');
  }
};

// 停止播放
const stopPlay = () => {
  try {
    if (webRtcServer) {
      webRtcServer.disconnect();
    }

    if (videoElement.value) {
      videoElement.value.style.display = 'none';
      videoElement.value.srcObject = null;
    }

    isPlaying.value = false;
    setStatus('视频播放已停止', 'info');

  } catch (error) {
    console.error('停止播放失败:', error);
    setStatus(`停止播放失败: ${(error as Error).message}`, 'error');
  }
};

// 设置状态信息
const setStatus = (message: string, type: 'success' | 'error' | 'info') => {
  statusMessage.value = message;
  
  switch (type) {
    case 'success':
      statusClass.value = 'bg-green-50 border border-green-200 text-green-700';
      break;
    case 'error':
      statusClass.value = 'bg-red-50 border border-red-200 text-red-700';
      break;
    case 'info':
      statusClass.value = 'bg-blue-50 border border-blue-200 text-blue-700';
      break;
  }
};

// 显示模态框
const showModal = (videoUrl?: string) => {
  if (videoUrl) {
    rtspUrl.value = videoUrl;
  }
  open.value = true;
  statusMessage.value = '';
};

// 关闭模态框
const handleCancel = () => {
  if (isPlaying.value) {
    stopPlay();
  }
  open.value = false;
  statusMessage.value = '';
};

// 清理函数
const cleanup = () => {
  if (webRtcServer) {
    try {
      webRtcServer.disconnect();
    } catch (error) {
      console.error('清理WebRTC连接失败:', error);
    }
    webRtcServer = null;
  }
};

defineExpose({
  showModal,
  cleanup
});
</script>

<style scoped>
/* 自定义样式 */
</style>
