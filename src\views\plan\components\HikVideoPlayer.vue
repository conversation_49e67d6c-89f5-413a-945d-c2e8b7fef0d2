<template>
  <j-modal
    :title="'WebRTC视频播放器'"
    :width="1000"
    v-model:open="open"
    :footer="null"
    @cancel="handleCancel"
    class="webrtc-video-modal"
  >
    <div class="p-6">
      <!-- 插件状态检测 -->
      <div class="mb-4">
        <div class="bg-blue-50 p-4 rounded-lg">
          <h3 class="text-lg font-semibold mb-2 flex items-center">
            <span class="mr-2">🔌</span>
            海康Web插件状态
          </h3>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div class="flex justify-between">
              <span>插件版本：</span>
              <span :class="pluginVersion ? 'text-green-600' : 'text-red-600'">
                {{ pluginVersion || '未检测到' }}
              </span>
            </div>
            <div class="flex justify-between">
              <span>插件状态：</span>
              <span :class="pluginLoaded ? 'text-green-600' : 'text-red-600'">
                {{ pluginLoaded ? '✅ 已加载' : '❌ 未加载' }}
              </span>
            </div>
          </div>
          
          <div v-if="!pluginLoaded" class="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
            <p class="text-yellow-800 text-sm">
              <strong>插件未加载：</strong>请确保已安装海康Web插件V1.5.5并在浏览器中启用
            </p>
            <div class="mt-2">
              <a 
                href="https://www.hikvision.com/cn/support/download/firmware/security-management-software/" 
                target="_blank"
                class="text-blue-600 hover:text-blue-800 text-xs"
              >
                下载海康Web插件
              </a>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频配置 -->
      <div class="mb-4">
        <h4 class="text-md font-medium mb-3">视频配置</h4>
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">设备IP地址</label>
            <input 
              v-model="videoConfig.ip" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="*************"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">端口</label>
            <input 
              v-model="videoConfig.port" 
              type="number" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="554"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
            <input 
              v-model="videoConfig.username" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="admin"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
            <input 
              v-model="videoConfig.password" 
              type="password" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="password"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">通道号</label>
            <input 
              v-model="videoConfig.channel" 
              type="number" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="1"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">码流类型</label>
            <select 
              v-model="videoConfig.streamType" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="0">主码流</option>
              <option value="1">子码流</option>
            </select>
          </div>
        </div>
        
        <div class="mt-4 flex gap-2">
          <button 
            @click="startPreview" 
            :disabled="!pluginLoaded || isPlaying"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors"
          >
            {{ isPlaying ? '播放中...' : '开始预览' }}
          </button>
          <button 
            @click="stopPreview" 
            :disabled="!pluginLoaded || !isPlaying"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-gray-400 transition-colors"
          >
            停止预览
          </button>
          <button 
            @click="captureImage" 
            :disabled="!pluginLoaded || !isPlaying"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors"
          >
            截图
          </button>
        </div>
      </div>

      <!-- 视频播放区域 -->
      <div class="mb-4">
        <h4 class="text-md font-medium mb-2">视频播放</h4>
        <div class="relative bg-black rounded-lg overflow-hidden" style="aspect-ratio: 16/9;">
          <!-- 海康插件容器 -->
          <div 
            ref="videoContainer"
            id="hikVideoPlayer"
            class="w-full h-full"
            style="background: #000;"
          >
            <!-- 插件将在这里渲染视频 -->
          </div>
          
          <!-- 加载状态 -->
          <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div class="text-white">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <div>{{ loadingText }}</div>
            </div>
          </div>
          
          <!-- 无插件提示 -->
          <div v-if="!pluginLoaded" class="absolute inset-0 flex items-center justify-center bg-gray-800">
            <div class="text-center text-white">
              <div class="text-4xl mb-4">🔌</div>
              <div class="text-lg mb-2">海康Web插件未加载</div>
              <div class="text-sm opacity-75">请安装并启用海康Web插件V1.5.5</div>
            </div>
          </div>
        </div>
        
        <!-- 错误信息 -->
        <div v-if="errorMessage" class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-700 text-sm">{{ errorMessage }}</p>
        </div>
        
        <!-- 成功信息 -->
        <div v-if="successMessage" class="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p class="text-green-700 text-sm">{{ successMessage }}</p>
        </div>
      </div>

      <!-- 操作日志 -->
      <div class="mb-4" v-if="operationLogs.length > 0">
        <h4 class="text-md font-medium mb-2">操作日志</h4>
        <div class="bg-gray-50 p-3 rounded-lg max-h-32 overflow-y-auto">
          <div 
            v-for="(log, index) in operationLogs" 
            :key="index"
            class="text-xs text-gray-600 mb-1"
          >
            <span class="text-gray-400">{{ log.time }}</span> - {{ log.message }}
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="border rounded-lg p-4">
        <h4 class="font-medium mb-2 flex items-center">
          <span class="mr-2">📖</span>
          使用说明
        </h4>
        <div class="text-sm text-gray-600 space-y-1">
          <p>• 确保已安装海康Web插件V1.5.5并在浏览器中启用</p>
          <p>• 输入正确的设备IP地址、端口、用户名和密码</p>
          <p>• 选择合适的通道号和码流类型</p>
          <p>• 点击"开始预览"按钮开始播放RTSP视频流</p>
          <p>• 支持截图功能，截图将保存到本地</p>
        </div>
      </div>
    </div>
  </j-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import JModal from "@/components/Modal/src/JModal/JModal.vue";

interface VideoConfig {
  ip: string;
  port: number;
  username: string;
  password: string;
  channel: number;
  streamType: number;
}

interface OperationLog {
  time: string;
  message: string;
}

const open = ref(false);
const videoContainer = ref<HTMLElement>();
const pluginLoaded = ref(false);
const pluginVersion = ref('');
const isPlaying = ref(false);
const loading = ref(false);
const loadingText = ref('');
const errorMessage = ref('');
const successMessage = ref('');
const operationLogs = ref<OperationLog[]>([]);

// 视频配置
const videoConfig = ref<VideoConfig>({
  ip: '*************',
  port: 554,
  username: 'admin',
  password: 'admin123',
  channel: 1,
  streamType: 0
});

// 海康插件对象
let hikPlugin: any = null;
let previewHandle: number = -1;

// 添加操作日志
const addLog = (message: string) => {
  const now = new Date();
  const timeStr = now.toLocaleTimeString();
  operationLogs.value.unshift({
    time: timeStr,
    message
  });
  
  // 保持最多20条日志
  if (operationLogs.value.length > 20) {
    operationLogs.value = operationLogs.value.slice(0, 20);
  }
};

// 检测海康插件
const checkHikPlugin = () => {
  try {
    // 检查是否存在海康插件对象
    if (window.WebVideoCtrl) {
      pluginLoaded.value = true;
      hikPlugin = window.WebVideoCtrl;
      
      // 获取插件版本
      try {
        pluginVersion.value = hikPlugin.I_GetVersion() || 'V1.5.5';
      } catch (e) {
        pluginVersion.value = 'V1.5.5';
      }
      
      addLog('海康Web插件加载成功');
      
      // 初始化插件
      initPlugin();
    } else {
      pluginLoaded.value = false;
      addLog('未检测到海康Web插件');
    }
  } catch (error) {
    console.error('检测海康插件失败:', error);
    pluginLoaded.value = false;
    addLog(`插件检测失败: ${error.message}`);
  }
};

// 初始化插件
const initPlugin = () => {
  if (!hikPlugin) return;
  
  try {
    // 设置播放容器
    hikPlugin.I_InitPlugin(800, 600, {
      bWndFull: true,
      iWndowType: 1,
      cbSelWnd: function(xmlDoc) {
        console.log('窗口选择回调:', xmlDoc);
      }
    });
    
    addLog('插件初始化完成');
  } catch (error) {
    console.error('插件初始化失败:', error);
    addLog(`插件初始化失败: ${error.message}`);
  }
};

// 开始预览
const startPreview = async () => {
  if (!hikPlugin || isPlaying.value) return;
  
  loading.value = true;
  loadingText.value = '正在连接设备...';
  errorMessage.value = '';
  successMessage.value = '';
  
  try {
    addLog(`开始连接设备 ${videoConfig.value.ip}:${videoConfig.value.port}`);
    
    // 登录设备
    const loginResult = await new Promise((resolve, reject) => {
      hikPlugin.I_Login(
        videoConfig.value.ip,
        1,
        videoConfig.value.port,
        videoConfig.value.username,
        videoConfig.value.password,
        {
          success: function(xmlDoc) {
            console.log('登录成功:', xmlDoc);
            resolve(xmlDoc);
          },
          error: function(status, xmlDoc) {
            console.error('登录失败:', status, xmlDoc);
            reject(new Error(`登录失败: ${status}`));
          }
        }
      );
    });
    
    addLog('设备登录成功');
    loadingText.value = '正在启动预览...';
    
    // 开始预览
    const previewResult = await new Promise((resolve, reject) => {
      previewHandle = hikPlugin.I_StartRealPlay(videoConfig.value.ip, {
        iRtspPort: videoConfig.value.port,
        iChannelID: videoConfig.value.channel,
        iStreamType: videoConfig.value.streamType,
        success: function(xmlDoc) {
          console.log('预览启动成功:', xmlDoc);
          resolve(xmlDoc);
        },
        error: function(status, xmlDoc) {
          console.error('预览启动失败:', status, xmlDoc);
          reject(new Error(`预览启动失败: ${status}`));
        }
      });
    });
    
    isPlaying.value = true;
    successMessage.value = '视频预览启动成功';
    addLog('视频预览启动成功');
    
  } catch (error) {
    console.error('启动预览失败:', error);
    errorMessage.value = `启动预览失败: ${error.message}`;
    addLog(`启动预览失败: ${error.message}`);
  } finally {
    loading.value = false;
    loadingText.value = '';
  }
};

// 停止预览
const stopPreview = () => {
  if (!hikPlugin || !isPlaying.value) return;
  
  try {
    if (previewHandle >= 0) {
      hikPlugin.I_Stop(previewHandle);
      previewHandle = -1;
    }
    
    // 登出设备
    hikPlugin.I_Logout(videoConfig.value.ip);
    
    isPlaying.value = false;
    successMessage.value = '视频预览已停止';
    addLog('视频预览已停止');
    
  } catch (error) {
    console.error('停止预览失败:', error);
    errorMessage.value = `停止预览失败: ${error.message}`;
    addLog(`停止预览失败: ${error.message}`);
  }
};

// 截图
const captureImage = () => {
  if (!hikPlugin || !isPlaying.value) return;
  
  try {
    const result = hikPlugin.I_CapturePic(previewHandle, 'screenshot.jpg');
    if (result) {
      successMessage.value = '截图成功，已保存到本地';
      addLog('截图成功');
    } else {
      errorMessage.value = '截图失败';
      addLog('截图失败');
    }
  } catch (error) {
    console.error('截图失败:', error);
    errorMessage.value = `截图失败: ${error.message}`;
    addLog(`截图失败: ${error.message}`);
  }
};

// 显示模态框
const showModal = () => {
  open.value = true;
  setTimeout(() => {
    checkHikPlugin();
  }, 100);
};

// 关闭模态框
const handleCancel = () => {
  // 停止预览
  if (isPlaying.value) {
    stopPreview();
  }
  
  open.value = false;
  errorMessage.value = '';
  successMessage.value = '';
  operationLogs.value = [];
};

// 组件挂载时检测插件
onMounted(() => {
  // 延迟检测，确保页面完全加载
  setTimeout(() => {
    checkHikPlugin();
  }, 1000);
});

// 组件销毁时清理
onBeforeUnmount(() => {
  if (isPlaying.value) {
    stopPreview();
  }
});

defineExpose({
  showModal
});

// 扩展Window接口以支持海康插件
declare global {
  interface Window {
    WebVideoCtrl: any;
  }
}
</script>

<style scoped>
.hik-video-modal :deep(.ant-modal-content) {
  border-radius: 16px;
}

.hik-video-modal :deep(.ant-modal-body) {
  padding: 0;
}

#hikVideoPlayer {
  position: relative;
}
</style>
