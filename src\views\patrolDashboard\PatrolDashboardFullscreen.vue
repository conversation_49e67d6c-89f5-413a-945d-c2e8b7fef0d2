<template>
  <div class="patrol-dashboard-fullscreen">
    <PatrolDashboard />
  </div>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from 'vue';
import PatrolDashboard from './PatrolDashboard.vue';

// 进入全屏模式
const enterFullscreen = () => {
  const element = document.documentElement;
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if ((element as any).webkitRequestFullscreen) {
    (element as any).webkitRequestFullscreen();
  } else if ((element as any).msRequestFullscreen) {
    (element as any).msRequestFullscreen();
  }
};

// 退出全屏模式
const exitFullscreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if ((document as any).webkitExitFullscreen) {
    (document as any).webkitExitFullscreen();
  } else if ((document as any).msExitFullscreen) {
    (document as any).msExitFullscreen();
  }
};

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  // ESC键退出全屏
  if (event.key === 'Escape') {
    exitFullscreen();
  }
  // F11键切换全屏
  if (event.key === 'F11') {
    event.preventDefault();
    if (document.fullscreenElement) {
      exitFullscreen();
    } else {
      enterFullscreen();
    }
  }
};

onMounted(() => {
  // 自动进入全屏
  setTimeout(() => {
    enterFullscreen();
  }, 500);
  
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown);
});

onUnmounted(() => {
  // 移除键盘事件监听
  document.removeEventListener('keydown', handleKeydown);
  
  // 退出全屏
  exitFullscreen();
});
</script>

<style lang="less" scoped>
.patrol-dashboard-fullscreen {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  
  :deep(.patrol-dashboard) {
    width: 100vw;
    height: 100vh;
  }
}
</style>
