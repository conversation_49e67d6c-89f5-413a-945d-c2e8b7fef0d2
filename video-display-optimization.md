# 视频监控显示优化

## 优化目标
当巡更计划暂无监控视频时，高级模式下的视频框不显示，避免显示空的视频监控区域。

## 优化前的问题
1. **视频监控组件总是显示**: 即使没有视频，PlanVideoMonitorModal组件也会被渲染
2. **空状态提示显示**: 只有在没有视频时才显示"暂无视频监控"提示
3. **资源浪费**: 空的视频监控组件仍然占用DOM和内存资源

## 优化后的逻辑

### 1. 视频监控卡片显示条件
视频监控整个卡片只在以下情况下显示：
- `videoMonitorLoading = true` (正在加载视频监控)
- `videoMonitorError` 存在 (加载出错)
- `info.lineVideoList` 存在且长度 > 0 (有视频数据)

### 2. 组件渲染逻辑
```vue
<!-- 视频监控 - 只在加载中、有错误或有视频时显示 -->
<div v-if="videoMonitorLoading || videoMonitorError || (info.lineVideoList && info.lineVideoList.length > 0)" class="lg:col-span-1">
  <!-- 视频监控卡片内容 -->
</div>
```

### 3. 内部状态显示
在视频监控卡片内部：
- **加载中**: 显示加载动画和提示
- **加载错误**: 显示错误信息和重新加载按钮
- **有视频**: 显示PlanVideoMonitorModal组件和播放提示
- **无视频**: 整个卡片不显示（优化后的行为）

## 代码变更

### PlanDetailModal.vue 模板部分
```vue
<!-- 优化前 -->
<div class="lg:col-span-1">
  <!-- 视频监控卡片总是显示 -->
  <div class="tech-card">
    <!-- PlanVideoMonitorModal总是渲染 -->
    <PlanVideoMonitorModal />
    <!-- 只有无视频时显示提示 -->
    <div v-else-if="!info.lineVideoList || info.lineVideoList.length === 0">
      暂无视频监控
    </div>
  </div>
</div>

<!-- 优化后 -->
<div v-if="videoMonitorLoading || videoMonitorError || (info.lineVideoList && info.lineVideoList.length > 0)" class="lg:col-span-1">
  <div class="tech-card">
    <!-- 只在有视频时渲染PlanVideoMonitorModal -->
    <template v-else-if="formattedVideoList.length > 0">
      <PlanVideoMonitorModal />
    </template>
  </div>
</div>
```

### 清理逻辑优化
```javascript
// 优化前
if (videoMonitorRef.value) {
  // 清理逻辑
}

// 优化后
if (videoMonitorRef.value && formattedVideoList.value.length > 0) {
  // 只在有视频时才清理
} else {
  console.log('无视频监控组件需要清理');
}
```

## 优化效果

### 1. 用户体验改善
- **界面更简洁**: 没有视频时不显示空的视频监控区域
- **减少困惑**: 用户不会看到空的视频播放器界面
- **布局优化**: 页面布局更加紧凑，空间利用更合理

### 2. 性能优化
- **减少DOM节点**: 无视频时不渲染视频监控组件
- **内存节省**: 避免创建不必要的Vue组件实例
- **减少计算**: 不需要处理空的视频列表

### 3. 维护性提升
- **逻辑清晰**: 显示条件更加明确
- **代码简洁**: 减少不必要的条件判断
- **错误处理**: 更好的错误状态处理

## 测试场景

### 1. 有视频的巡更计划
- ✅ 显示视频监控卡片
- ✅ 显示PlanVideoMonitorModal组件
- ✅ 显示视频播放提示

### 2. 无视频的巡更计划
- ✅ 不显示视频监控卡片
- ✅ 不渲染PlanVideoMonitorModal组件
- ✅ 页面布局正常

### 3. 加载状态
- ✅ 显示视频监控卡片
- ✅ 显示加载动画
- ✅ 加载完成后根据结果显示或隐藏

### 4. 错误状态
- ✅ 显示视频监控卡片
- ✅ 显示错误信息
- ✅ 提供重新加载按钮

## 兼容性说明
- 保持了原有的加载和错误处理逻辑
- 不影响现有的视频播放功能
- 向后兼容，不会破坏现有功能

## 总结
通过这次优化，实现了更智能的视频监控显示逻辑：
1. **按需显示**: 只在需要时显示视频监控区域
2. **资源优化**: 减少不必要的组件渲染
3. **用户体验**: 界面更加简洁和直观
4. **代码质量**: 逻辑更清晰，维护性更好
