import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/masterPlan/list',
  save='/masterPlan/add',
  edit='/masterPlan/edit',
  deleteOne = '/masterPlan/delete',
  deleteBatch = '/masterPlan/deleteBatch',
  importExcel = '/masterPlan/importExcel',
  exportXls = '/masterPlan/exportXls',
  updateStatus = '/masterPlan/updateStatus',
  getRelatedPlans = '/masterPlan/getRelatedPlans',
  checkMasterPlanName = '/masterPlan/checkMasterPlanName',
  getCurrentPlanByLineId = '/masterPlan/getCurrentPlanByLineId',
  getMasterPlanByLineId = '/masterPlan/getMasterPlanByLineId',
  setCurrentPlan = '/masterPlan/setCurrentPlan',
  autoUpdateCurrentPlan = '/masterPlan/autoUpdateCurrentPlan',
  getPatrolRecords = '/masterPlan/getPatrolRecords',
  getPatrolConfig = '/patrolConfig/patrolConfig/queryById',
}

/**
 * 导出api
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 */
export const list = (params) => defHttp.get({url: Api.list, params});

/**
 * 删除单个
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 */
export const deleteBatch = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({url: url, params}, {isTransformResponse: false});
}

/**
 * 启用/禁用总计划
 */
export const updateStatus = (id: string, status: string) => {
  return defHttp.post({
    url: Api.updateStatus,
    params: { id, status }
  });
}

/**
 * 获取关联的巡更计划列表
 */
export const getRelatedPlans = (masterPlanId: string) => {
  return defHttp.get({
    url: Api.getRelatedPlans,
    params: { masterPlanId }
  });
}

/**
 * 验证总计划名称是否重复
 */
export const checkMasterPlanName = (masterPlanName: string, id?: string) => {
  return defHttp.get({
    url: Api.checkMasterPlanName,
    params: { masterPlanName, id }
  });
}

/**
 * 根据路线ID获取当前执行的巡更计划
 */
export const getCurrentPlanByLineId = (lineId: string) => {
  return defHttp.get({
    url: Api.getCurrentPlanByLineId,
    params: { lineId }
  });
}

/**
 * 根据路线ID获取对应的总计划
 */
export const getMasterPlanByLineId = (lineId: string) => {
  return defHttp.get({
    url: Api.getMasterPlanByLineId,
    params: { lineId }
  });
}

/**
 * 设置当前执行的巡更计划
 */
export const setCurrentPlan = (masterPlanId: string, planId: string) => {
  return defHttp.post({
    url: Api.setCurrentPlan,
    params: { masterPlanId, planId }
  });
}

/**
 * 自动更新当前执行的巡更计划
 */
export const autoUpdateCurrentPlan = (lineId: string) => {
  return defHttp.post({
    url: Api.autoUpdateCurrentPlan,
    params: { lineId }
  });
}

/**
 * 获取总计划下的巡更记录
 */
export const getPatrolRecords = (masterPlanId: string) => {
  return defHttp.get({
    url: Api.getPatrolRecords,
    params: { masterPlanId }
  });
}

/**
 * 获取巡更配置信息
 */
export const getPatrolConfig = () => {
  return defHttp.get({
    url: Api.getPatrolConfig,
    params: { id: 1 }
  });
}
