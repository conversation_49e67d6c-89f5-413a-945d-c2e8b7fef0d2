import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { JVxeTypes,JVxeColumn } from '/@/components/jeecg/JVxeTable/types';
import { h } from 'vue';

// 简洁科技感数据显示组件
const renderTechData = (value: number | string, type: 'count' | 'rate' = 'count', status?: 'normal' | 'warning' | 'danger') => {
  const numValue = typeof value === 'string' ? parseFloat(value) || 0 : (value || 0);

  // 根据类型确定显示格式
  const displayValue = type === 'rate' ? `${numValue}%` : numValue.toString();

  // 根据状态确定颜色
  let textColor = '#1890ff'; // 默认蓝色
  let bgColor = 'rgba(24, 144, 255, 0.1)';
  let borderColor = 'rgba(24, 144, 255, 0.3)';

  if (status === 'warning') {
    textColor = '#fa8c16';
    bgColor = 'rgba(250, 140, 22, 0.1)';
    borderColor = 'rgba(250, 140, 22, 0.3)';
  }
  if (status === 'danger') {
    textColor = '#f5222d';
    bgColor = 'rgba(245, 34, 45, 0.1)';
    borderColor = 'rgba(245, 34, 45, 0.3)';
  }

  return h('div', {
    style: {
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '4px 12px',
      background: bgColor,
      border: `1px solid ${borderColor}`,
      borderRadius: '4px',
      fontFamily: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
      fontSize: '13px',
      fontWeight: '600',
      color: textColor,
      minWidth: '50px',
      transition: 'all 0.2s ease',
    }
  }, displayValue);
};

// 简洁进度条样式的巡更率显示
const renderPatrolRate = (rate: number | string) => {
  const numRate = typeof rate === 'string' ? parseFloat(rate) || 0 : (rate || 0);

  // 根据巡更率确定状态和颜色
  let progressColor = '#52c41a'; // 绿色 - 正常
  let textColor = '#52c41a';
  let bgColor = 'rgba(82, 196, 26, 0.1)';

  if (numRate < 60) {
    progressColor = '#f5222d'; // 红色 - 危险
    textColor = '#f5222d';
    bgColor = 'rgba(245, 34, 45, 0.1)';
  } else if (numRate < 80) {
    progressColor = '#fa8c16'; // 橙色 - 警告
    textColor = '#fa8c16';
    bgColor = 'rgba(250, 140, 22, 0.1)';
  }

  return h('div', {
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      width: '100%',
    }
  }, [
    // 进度条容器
    h('div', {
      style: {
        flex: 1,
        height: '16px',
        background: '#f5f5f5',
        borderRadius: '8px',
        border: '1px solid #d9d9d9',
        overflow: 'hidden',
        position: 'relative',
      }
    }, [
      // 进度条
      h('div', {
        style: {
          width: `${numRate}%`,
          height: '100%',
          background: progressColor,
          borderRadius: '8px',
          transition: 'width 0.3s ease',
        }
      })
    ]),
    // 数值显示
    h('span', {
      style: {
        fontFamily: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
        fontSize: '12px',
        fontWeight: '600',
        color: textColor,
        minWidth: '40px',
        textAlign: 'right',
        padding: '2px 6px',
        background: bgColor,
        borderRadius: '4px',
        border: `1px solid ${progressColor}33`,
      }
    }, `${numRate}%`)
  ]);
};

//列表数据
export const columns: BasicColumn[] = [
  {
    title: '总计划名称',
    align: "center",
    dataIndex: 'masterPlanName',
    width: 350,
    ellipsis: true
  },
  // {
  //   title: '监区',
  //   align: "center",
  //   dataIndex: 'lineName',
  //   width: 120,
  //   ellipsis: true
  // },
 
 
  {
    title: '已执行计划次数',
    align: "center",
    dataIndex: 'executedPlanCount',
    width: 140,
    customRender: ({text}) => {
      return renderTechData(text || 0, 'count');
    }
  },
  {
    title: '总巡更点数',
    align: "center",
    dataIndex: 'totalPatrolPoints',
    width: 120,
    customRender: ({text}) => {
      return renderTechData(text || 0, 'count');
    }
  },
  {
    title: '正常巡更点数',
    align: "center",
    dataIndex: 'normalPatrolPoints',
    width: 130,
    customRender: ({text}) => {
      return renderTechData(text || 0, 'count', 'normal');
    }
  },
  {
    title: '待巡点数',
    align: "center",
    dataIndex: 'pendingPatrolPoints',
    width: 110,
    customRender: ({text}) => {
      const value = text || 0;
      const status = value > 0 ? 'warning' : 'normal';
      return renderTechData(value, 'count', status);
    }
  },
  {
    title: '漏巡点数',
    align: "center",
    dataIndex: 'missedPatrolPoints',
    width: 110,
    customRender: ({text}) => {
      const value = text || 0;
      const status = value > 0 ? 'danger' : 'normal';
      return renderTechData(value, 'count', status);
    }
  },
  {
    title: '正常巡更率',
    align: "center",
    dataIndex: 'normalPatrolRate',
    width: 180,
    customRender: ({text}) => {
      const rate = typeof text === 'string' ? parseFloat(text) || 0 : (text || 0);
      return renderPatrolRate(rate);
    }
  },
 
  // {
  //   title: '创建日期',
  //   align: "center",
  //   dataIndex: 'createTime',
  //   width: 160,
  //   ellipsis: true
  // },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
  // {
  //   label: "总计划名称",
  //   field: 'masterPlanName',
  //   component: 'Input',
  //   colProps: {span: 6},
  // },
  {
    label: "日期",
    field: 'timeRange',
    component: 'RangePicker',
    componentProps: {
      format: 'YYYY-MM-DD',
      placeholder: ['开始日期', '结束日期'],
      valueFormat: 'YYYY-MM-DD',
      onChange: (dates: any, dateStrings: string[]) => {
        // 日期选择后自动触发查询
        if (dates && dates.length === 2 && dateStrings && dateStrings.length === 2) {
          // 直接触发查询，表单值会自动更新
          setTimeout(() => {
            const event = new CustomEvent('dateRangeChange', {
              detail: {
                dates: dates,
                dateStrings: dateStrings
              }
            });
            window.dispatchEvent(event);
          }, 100);
        }
      }
    },
    colProps: {span: 8},
  },
  // {
  //   label: "状态",
  //   field: 'status',
  //   component: 'JDictSelectTag',
  //   componentProps:{
  //     dictCode: "master_plan_status",
  //     placeholder: '请选择状态',
  //     stringToNumber: false,
  //   },
  //   colProps: {span: 6},
  // },
];

//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '总计划名称',
    field: 'masterPlanName',
    component: 'Input',
    required: true,
    dynamicRules: ({model,schema}) => {
      return [
        { required: true, message: '请输入总计划名称!'},
        { max: 100, message: '总计划名称不能超过100个字符!'},
      ];
    },
  },
  {
    label: '路线',
    field: 'lineId',
    component: 'JDictSelectTag',
    required: true,
    componentProps:{
      dictCode: "patrol_line,name,id",
      placeholder: '请选择路线',
      stringToNumber: false,
    },
    dynamicRules: ({model,schema}) => {
      return [
        { required: true, message: '请选择路线!'},
      ];
    },
  },
  {
    label: '当前巡更计划',
    field: 'currentPlanId',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode: "patrol_plan,name,id",
      placeholder: '请选择当前巡更计划',
      stringToNumber: false,
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'JDictSelectTag',
    required: true,
    componentProps:{
      dictCode: "master_plan_status",
      placeholder: '请选择状态',
      stringToNumber: false,
    },
    dynamicRules: ({model,schema}) => {
      return [
        { required: true, message: '请选择状态!'},
      ];
    },
  },
  {
    label: '描述',
    field: 'description',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入描述',
      rows: 3,
      maxlength: 500,
      showCount: true,
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false
  },
];
