# 分监区可视化数据大屏

## 功能概述

这是一个专为监狱分监区设计的可视化数据大屏页面，具有科技感的响应式界面设计，实时显示巡更相关信息。支持全屏显示和多种屏幕尺寸适配。

## 主要功能

### 1. 分监区信息展示
- 显示分监区名称
- 实时时间显示
- 科技感的标题设计

### 2. 今日巡更人员信息
- 显示3个巡更人员的基本信息
- 包含人员正方形照片、姓名、卡号
- 实时状态指示（在线/离线/值班中）
- 值班人员高亮显示和脉冲动画
- 响应式照片尺寸适配

### 3. 实时巡更计划状态
- 可滚动的巡更计划列表
- 按状态排序：进行中 > 待开始 > 已完成 > 已漏巡
- 显示监区名称、巡更人员、时间信息
- 12个巡更点状态可视化展示
- 动画效果标识当前位置和下一个目标
- 巡更路线：活动室→1号室→2号室→...→10号室→活动室

### 4. 巡更点状态系统（已优化 v2.5）
- **已巡更**：绿色渐变圆圈，打勾图标，发光效果，白色边框
- **当前位置**：蓝色渐变圆圈，位置图标，脉冲动画，放大显示，白色边框
- **下一个目标**：黄色渐变圆圈，时钟图标，闪烁动画，高亮文字
- **待巡更**：黄色渐变圆圈，时钟图标，白色边框
- **漏巡**：红色渐变圆圈，叉号图标，发光效果，白色边框

### 5. 连接线系统（彩色版 v2.9）
- 直接连接两个打卡点，精确对齐
- 基于打卡点图标高度居中显示
- 适中的箭头指示器，清晰显示方向
- 智能颜色系统：根据打卡点状态显示不同颜色
- 当前位置连接线：蓝色到黄色渐变（当前→下一个）
- 已完成连接线：绿色渐变
- 默认连接线：青色渐变

### 6. 状态颜色优化（v2.5）
- **进行中状态**：蓝色主题 (#1890ff)
- **已完成状态**：绿色主题 (#52c41a)
- **待开始状态**：黄色主题 (#faad14)
- **异常状态**：红色主题 (#ff4d4f)

### 5. 全屏显示功能
- 一键全屏按钮，隐藏系统菜单和导航
- 自动适配全屏尺寸
- ESC或F11键退出全屏
- 全屏状态指示

## 页面访问

### 路由地址
- 首页：`/patrol/index`
- 普通模式：`/patrol/dashboard`
- 全屏模式：`/patrol/fullscreen`

### 访问方式
1. **普通模式**：在系统布局中查看，保留菜单和导航
2. **全屏模式**：自动全屏显示，适合投影或大屏展示

## 技术特点

### 1. 响应式设计
- 固定尺寸：1920×1080px
- 适配大屏显示设备
- 科技感视觉效果

### 2. 实时数据更新
- 每30秒自动刷新数据
- 实时时间显示
- 动态状态更新

### 3. 动画效果
- 人员状态脉冲动画
- 巡更点状态动画
- 页面元素过渡效果
- 悬停交互效果

### 4. 科技感设计
- 深色主题配色
- 渐变背景效果
- 发光边框和阴影
- 霓虹色彩搭配

## 数据接口

### API 端点
- `GET /patrol/dashboard/data` - 获取大屏数据
- `GET /patrol/dashboard/todayStaff` - 获取今日人员
- `GET /patrol/dashboard/patrolPlans` - 获取巡更计划

### 数据结构
```typescript
interface DashboardData {
  sectionName: string;           // 分监区名称
  todayStaff: StaffInfo[];       // 今日人员
  patrolPlans: PatrolPlan[];     // 巡更计划
  statistics?: Statistics;        // 统计信息
}
```

## 使用说明

### 1. 开发环境
```bash
# 启动开发服务器
npm run dev

# 访问页面
http://localhost:3000/patrol/index
```

### 2. 生产部署
- 确保API接口正常
- 配置正确的后端地址
- 部署到Web服务器

### 3. 全屏展示
- 访问 `/patrol/fullscreen` 路由
- 页面会自动进入全屏模式
- 按ESC或F11键可退出全屏

## 自定义配置

### 1. 修改分监区名称
在 `PatrolDashboard.vue` 中修改默认值：
```typescript
const sectionName = ref('第一分监区');
```

### 2. 调整刷新频率
修改数据刷新间隔：
```typescript
dataTimer = setInterval(loadData, 30000); // 30秒
```

### 3. 自定义样式
在 `PatrolDashboard.vue` 的 `<style>` 部分修改：
- 颜色主题
- 动画效果
- 布局尺寸

## 注意事项

1. **浏览器兼容性**：建议使用Chrome、Firefox等现代浏览器
2. **网络要求**：需要稳定的网络连接以获取实时数据
3. **显示设备**：最佳显示效果需要1920×1080分辨率
4. **性能优化**：长时间运行建议定期刷新页面

## 故障排除

### 1. 数据不显示
- 检查API接口是否正常
- 查看浏览器控制台错误信息
- 确认网络连接状态

### 2. 样式异常
- 清除浏览器缓存
- 检查CSS文件是否正确加载
- 确认浏览器版本支持

### 3. 动画不流畅
- 检查设备性能
- 关闭其他占用资源的程序
- 降低动画复杂度

### 4. 巡更点显示问题（已修复）
- ✅ 修复了巡更点图标不显示的问题
- ✅ 修复了连接线位置错误的问题
- ✅ 优化了巡更点状态样式
- ✅ 改进了响应式布局

### 5. 连接线问题（已修复）
- ✅ 重新设计了连接线样式
- ✅ 修复了连接线位置计算错误
- ✅ 添加了渐变和发光效果
- ✅ 确保最后一个点不显示连接线

## 最新更新 (v2.8)

### 静态连接线和打卡点动画优化
1. **连接线静态化**
   - ✅ 移除连接线流动动画，提升性能
   - ✅ 基于打卡点图标高度精确居中
   - ✅ 保持箭头指示器和发光效果
   - ✅ 简洁静态设计，更加专业

2. **打卡点动画增强**
   - ✅ 当前位置：脉冲动画 + 文字发光效果
   - ✅ 下一个目标：闪烁动画 + 文字闪烁效果
   - ✅ 图标和文字同步动画
   - ✅ 蓝色和黄色主题动画

3. **性能和视觉优化**
   - ✅ 减少不必要的动画，提升性能
   - ✅ 重点突出当前和下一个打卡点
   - ✅ 连接线基于图标中心精确定位
   - ✅ 整体视觉效果更加协调

## 历史更新 (v2.7)

### 连接线直接连接优化
1. **精确连接两个打卡点**
   - ✅ 从当前点图标右边缘开始
   - ✅ 延伸到下一个点左边缘
   - ✅ 基于巡更点中心垂直居中
   - ✅ 动态计算连接线长度

2. **恢复简洁设计风格**
   - ✅ 移除高铁轨道样式（用户反馈太丑）
   - ✅ 恢复原有的简洁连接线设计
   - ✅ 保持适中的箭头大小（6px）
   - ✅ 3px高度的连接线，视觉效果适中

3. **优化动画效果**
   - ✅ 恢复经典的流动光点动画
   - ✅ 2秒循环，节奏适中
   - ✅ 透明度渐变，营造流动感
   - ✅ 发光效果增强视觉吸引力

## 历史更新 (v2.6)

### 高铁样式连接线系统（已废弃）
1. **高铁运行图样式设计**
   - ❌ 底部轨道设计，所有巡更点连成一线（用户反馈太丑）
   - ❌ 长连接线段，从点中心延伸到下一个点
   - ❌ 更粗的连接线（5px高度），视觉效果过重
   - ❌ 立体渐变和内阴影，过于复杂

2. **大箭头指示系统**
   - ❌ 8px大箭头，过大影响美观
   - ❌ 箭头发光效果，过于突出
   - ❌ 精确定位在连接线右端
   - ❌ 清晰显示巡更方向

3. **高铁流动动画**
   - ❌ 模拟列车运行的流动效果，不符合预期
   - ❌ 30%宽度的光带，过于醒目
   - ❌ 3秒循环动画，节奏偏慢
   - ❌ 透明度渐变，营造真实感

## 历史更新 (v2.5)

### 箭头连接线和状态颜色优化
1. **箭头连接线系统**
   - ✅ 添加箭头指向，清晰显示巡更方向
   - ✅ 流动动画效果，光点从左到右移动
   - ✅ 箭头位置精确，指向下一个巡更点
   - ✅ 连接线基于巡更点中心垂直居中
   - ✅ 发光效果和渐变色设计

2. **状态颜色重新设计**
   - ✅ 进行中状态：改为蓝色主题 (#1890ff → #40a9ff)
   - ✅ 已完成状态：保持绿色主题 (#52c41a → #73d13d)
   - ✅ 当前位置：改为蓝色主题，与进行中状态一致
   - ✅ 已巡更点：保持绿色主题，表示完成状态

3. **动画效果优化**
   - ✅ 当前位置脉冲动画改为蓝色发光
   - ✅ 连接线流动动画，2秒循环
   - ✅ 箭头发光效果，增强视觉指向性
   - ✅ 透明度渐变，营造流动感

## 历史更新 (v2.4)

### 字体和连接线优化
1. **字体大小优化**
   - ✅ 摘要信息字体：从 10-12px 增大到 12-14px
   - ✅ 巡更点名称：从 8-10px 增大到 10-12px
   - ✅ 巡更点时间：从 7-8px 增大到 9-10px
   - ✅ 图标字体：从 8-12px 增大到 10-16px
   - ✅ 状态徽章：从 9-11px 增大到 11-13px

2. **图标尺寸优化**
   - ✅ 巡更点图标：从 20-28px 增大到 24-32px
   - ✅ 微型头像：从 16-20px 增大到 18-24px
   - ✅ 增加图标间距和内边距
   - ✅ 提升整体视觉效果

3. **连接线简化**
   - ✅ 移除流动动画效果，提升性能
   - ✅ 连接线真正连接两个巡更点
   - ✅ 基于整个巡更点中心垂直居中
   - ✅ 简洁的渐变设计，保持科技感

## 历史更新 (v2.3)

### 列表布局重构
1. **一行信息显示**
   - ✅ 监区名称、巡更者、开始时间、结束时间、用时在一行显示
   - ✅ 紧凑的信息布局，提高空间利用率
   - ✅ 条件显示：结束时间和用时仅在相关状态下显示
   - ✅ 微型头像集成到巡更者信息中

2. **巡更点铺满显示**
   - ✅ 巡更点自动平均分配整个列表宽度
   - ✅ 支持不同数量的巡更点（6个、8个、12个等）
   - ✅ 响应式布局，自适应容器宽度
   - ✅ 移除固定间距，使用flex布局

3. **连接线精确居中**
   - ✅ 连接线基于巡更点图标高度居中显示
   - ✅ 精确的位置计算，完美对齐
   - ✅ 缩短连接线长度，更加精致
   - ✅ 优化动画效果，提升视觉体验

## 历史更新 (v2.2)

### 列表样式优化
1. **巡更计划列表紧凑化**
   - ✅ 缩小列表项高度，减少内边距和间距
   - ✅ 移除滚动条，所有计划在页面内显示
   - ✅ 优化字体大小，提高信息密度
   - ✅ 减小图标和头像尺寸

2. **巡更点显示优化**
   - ✅ 所有12个打卡点响应式显示在列表内
   - ✅ 隐藏横向滚动条，保持滚动功能
   - ✅ 优化巡更点间距和尺寸
   - ✅ 减小图标尺寸，提高显示密度

3. **连接线精确定位**
   - ✅ 连接线真正连接两个打卡点中心
   - ✅ 上下居中显示，完美对齐
   - ✅ 减小连接线尺寸，更加精致
   - ✅ 优化流动动画效果

## 历史更新 (v2.1)

### 颜色方案优化
1. **巡更点颜色重新设计**
   - ✅ 已巡更：改为绿色渐变 (#52c41a → #73d13d)
   - ✅ 待巡更：改为黄色渐变 (#faad14 → #ffc53d)
   - ✅ 漏巡：改为红色渐变 (#ff4d4f → #ff7875)
   - ✅ 当前位置：绿色渐变加强发光效果

2. **连接线系统完全重构**
   - ✅ 修复连接线位置，真正连接两个点
   - ✅ 重新设计连接线定位算法
   - ✅ 添加流动光效动画
   - ✅ 优化连接线的视觉效果

3. **视觉一致性改进**
   - ✅ 统一颜色方案：绿色(正常)、黄色(待处理)、红色(异常)
   - ✅ 优化文字颜色与图标颜色的匹配
   - ✅ 改进动画效果的颜色过渡

## 历史更新 (v2.0)

### 修复内容
1. **巡更点显示优化**
   - 重新设计了巡更点的CSS类名结构
   - 修复了图标不显示的问题
   - 优化了不同状态的视觉效果

2. **连接线系统重构**
   - 重新设计了连接线的HTML结构
   - 修复了连接线位置计算问题
   - 添加了渐变色和发光效果

3. **响应式改进**
   - 优化了不同屏幕尺寸下的显示效果
   - 改进了巡更点的间距和布局
   - 确保在各种设备上都能正常显示
