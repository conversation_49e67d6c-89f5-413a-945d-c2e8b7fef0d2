<template>
  <BasicModal
    title="RTSP视频流测试播放器"
    :width="1000"
    v-model:open="open"
    @cancel="handleCancel"
    class="rtsp-test-modal"
  >
    <div class="p-4">
      <!-- RTSP地址输入区域 -->
      <div class="mb-4">
        <h4 class="text-md font-medium mb-3">🔧 RTSP视频流测试</h4>
        <div class="space-y-3">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              RTSP视频流地址：
            </label>
            <div class="flex gap-2">
              <input 
                v-model="rtspUrl" 
                type="text" 
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                placeholder="rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream"
                @keyup.enter="testConnection"
              />
              <button 
                @click="testConnection" 
                :disabled="!rtspUrl || loading"
                class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors text-sm"
              >
                {{ loading ? '测试中...' : '测试连接' }}
              </button>
            </div>
          </div>
          
          <!-- 预设RTSP地址 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              预设地址（点击使用）：
            </label>
            <div class="grid grid-cols-1 gap-2 max-h-32 overflow-y-auto">
              <button 
                v-for="(preset, index) in presetUrls" 
                :key="index"
                @click="usePresetUrl(preset.url)"
                class="text-left p-2 border border-gray-200 rounded-md hover:bg-gray-50 transition-colors"
              >
                <div class="font-medium text-sm text-gray-800">{{ preset.name }}</div>
                <div class="text-xs text-gray-500 font-mono">{{ preset.url }}</div>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 连接状态 -->
      <div class="mb-4 p-3 bg-gray-50 rounded-lg">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div 
              :class="[
                'w-3 h-3 rounded-full mr-2',
                connectionStatus === 'connected' ? 'bg-green-500' : 
                connectionStatus === 'connecting' ? 'bg-yellow-500' : 'bg-red-500'
              ]"
            ></div>
            <span class="text-sm font-medium">{{ getStatusText() }}</span>
          </div>
          <div class="text-xs text-gray-500">
            {{ connectionStatus === 'connected' ? '可以开始播放' : '等待连接' }}
          </div>
        </div>
      </div>

      <!-- 视频播放区域 -->
      <div class="mb-4">
        <div class="relative bg-black rounded-lg overflow-hidden" style="aspect-ratio: 16/9;">
          <!-- WebRTC视频播放 -->
          <video
            ref="videoElement"
            id="rtspVideo"
            autoplay
            muted
            playsinline
            controls
            class="w-full h-full object-cover"
            style="display: none;"
          >
            您的浏览器不支持视频播放
          </video>

          <!-- 加载状态覆盖层 -->
          <div v-if="loading" class="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50 z-10">
            <div class="text-center text-white">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2"></div>
              <div>{{ loadingText }}</div>
            </div>
          </div>

          <!-- 状态显示区域 -->
          <div v-if="!isPlaying && !loading" class="w-full h-full flex items-center justify-center">
            <div v-if="connectionStatus === 'connected'" class="text-center text-white">
              <div class="text-4xl mb-4">✅</div>
              <div class="text-lg mb-2">RTSP连接成功</div>
              <div class="text-sm opacity-75">点击播放按钮开始视频播放</div>
            </div>

            <div v-else-if="connectionStatus === 'failed'" class="text-center text-white">
              <div class="text-4xl mb-4">❌</div>
              <div class="text-lg mb-2">连接失败</div>
              <div class="text-sm opacity-75">{{ errorMessage || '请检查RTSP地址和网络连接' }}</div>
              <button
                @click="retryConnection"
                class="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
              >
                重试连接
              </button>
            </div>

            <div v-else class="text-center text-white">
              <div class="text-4xl mb-4">🎥</div>
              <div class="text-lg mb-2">RTSP视频流播放器</div>
              <div class="text-sm opacity-75">输入RTSP地址并点击播放</div>
            </div>
          </div>
        </div>
        
        <!-- 错误信息 -->
        <div v-if="errorMessage" class="mt-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p class="text-red-700 text-sm">{{ errorMessage }}</p>
        </div>
        
        <!-- 成功信息 -->
        <div v-if="successMessage" class="mt-2 p-3 bg-green-50 border border-green-200 rounded-lg">
          <p class="text-green-700 text-sm">{{ successMessage }}</p>
        </div>
      </div>

      <!-- 控制按钮 -->
      <div class="flex justify-center space-x-3 mb-4">
        <button
          @click="testConnection"
          :disabled="!rtspUrl || loading"
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 transition-colors flex items-center"
        >
          <span class="mr-1">🔗</span>
          {{ loading ? '测试中...' : '测试连接' }}
        </button>
        <button
          v-if="!isPlaying"
          @click="startPlay"
          :disabled="!rtspUrl || loading"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors flex items-center"
        >
          <span class="mr-1">▶️</span>
          开始播放
        </button>
        <button
          v-if="isPlaying"
          @click="stopPlay"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors flex items-center"
        >
          <span class="mr-1">⏹️</span>
          停止播放
        </button>
        <button
          @click="resetTest"
          class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors flex items-center"
        >
          <span class="mr-1">🔄</span>
          重置
        </button>
      </div>

      <!-- RTSP地址解析信息 -->
      <div v-if="parsedInfo" class="p-3 bg-blue-50 rounded-lg">
        <div class="text-sm text-blue-800">
          <div class="font-medium mb-2">RTSP地址解析结果：</div>
          <div class="grid grid-cols-2 gap-2 text-xs">
            <div><strong>协议：</strong> {{ parsedInfo.protocol }}</div>
            <div><strong>主机：</strong> {{ parsedInfo.hostname }}</div>
            <div><strong>端口：</strong> {{ parsedInfo.port }}</div>
            <div><strong>用户名：</strong> {{ parsedInfo.username || '未设置' }}</div>
            <div><strong>路径：</strong> {{ parsedInfo.pathname }}</div>
            <div><strong>查询参数：</strong> {{ parsedInfo.search || '无' }}</div>
          </div>
        </div>
      </div>

      <!-- 使用说明 -->
      <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div class="text-sm text-yellow-800">
          <div class="font-medium mb-1">📖 使用说明：</div>
          <ul class="text-xs space-y-1">
            <li>• 输入有效的RTSP地址，格式：rtsp://用户名:密码@IP地址:端口/路径</li>
            <li>• 点击"测试连接"验证地址格式和可达性</li>
            <li>• 实际播放需要WebRTC信令服务器支持</li>
            <li>• 可以使用预设地址进行快速测试</li>
          </ul>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { BasicModal } from '/@/components/Modal';

interface ParsedRTSPInfo {
  protocol: string;
  hostname: string;
  port: string;
  username?: string;
  password?: string;
  pathname: string;
  search?: string;
}

const open = ref(false);
const loading = ref(false);
const loadingText = ref('');
const errorMessage = ref('');
const successMessage = ref('');
const connectionStatus = ref<'disconnected' | 'connecting' | 'connected' | 'failed'>('disconnected');
const parsedInfo = ref<ParsedRTSPInfo | null>(null);
const isPlaying = ref(false);
const videoElement = ref<HTMLVideoElement | null>(null);

// WebRTC-Streamer实例
let webRtcServer: any = null;

// RTSP视频流地址
const rtspUrl = ref('rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream');

// 预设RTSP地址
const presetUrls = ref([
  {
    name: '海康威视标准格式',
    url: 'rtsp://admin:admin123@*************:554/Streaming/Channels/101'
  },
  {
    name: '海康威视主码流',
    url: 'rtsp://admin:admin123@*************:554/h264/ch1/main/av_stream'
  },
  {
    name: '海康威视子码流',
    url: 'rtsp://admin:admin123@*************:554/h264/ch1/sub/av_stream'
  },
  {
    name: '大华标准格式',
    url: 'rtsp://admin:admin123@*************:554/cam/realmonitor?channel=1&subtype=0'
  },
  {
    name: '通用RTSP格式',
    url: 'rtsp://admin:admin123@*************:554/live/ch1'
  },
  {
    name: '无认证格式',
    url: 'rtsp://*************:554/stream1'
  }
]);

// 获取状态文本
const getStatusText = () => {
  switch (connectionStatus.value) {
    case 'connected': return '✅ 连接成功';
    case 'connecting': return '🔄 连接中';
    case 'failed': return '❌ 连接失败';
    default: return '⚪ 未连接';
  }
};

// 使用预设URL
const usePresetUrl = (url: string) => {
  rtspUrl.value = url;
  successMessage.value = `已选择预设地址`;
  errorMessage.value = '';
  connectionStatus.value = 'disconnected';
  parsedInfo.value = null;
};

// 解析RTSP URL
const parseRTSPUrl = (url: string): ParsedRTSPInfo | null => {
  try {
    const urlObj = new URL(url);
    return {
      protocol: urlObj.protocol,
      hostname: urlObj.hostname,
      port: urlObj.port || '554',
      username: urlObj.username,
      password: urlObj.password,
      pathname: urlObj.pathname,
      search: urlObj.search
    };
  } catch (error) {
    return null;
  }
};

// 测试连接
const testConnection = async () => {
  if (!rtspUrl.value) {
    errorMessage.value = '请输入RTSP地址';
    return;
  }
  
  // 验证RTSP URL格式
  if (!rtspUrl.value.startsWith('rtsp://')) {
    errorMessage.value = 'RTSP地址必须以 rtsp:// 开头';
    return;
  }
  
  loading.value = true;
  loadingText.value = '正在解析RTSP地址...';
  connectionStatus.value = 'connecting';
  errorMessage.value = '';
  successMessage.value = '';
  
  try {
    // 解析URL
    const parsed = parseRTSPUrl(rtspUrl.value);
    if (!parsed) {
      throw new Error('RTSP地址格式错误');
    }
    
    parsedInfo.value = parsed;
    loadingText.value = '正在测试网络连通性...';
    
    // 模拟网络测试（实际项目中这里会进行真实的连接测试）
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 模拟连接结果（实际项目中根据真实测试结果）
    const isReachable = Math.random() > 0.3; // 70%成功率模拟
    
    if (isReachable) {
      connectionStatus.value = 'connected';
      successMessage.value = `RTSP连接测试成功！服务器: ${parsed.hostname}:${parsed.port}`;
    } else {
      connectionStatus.value = 'failed';
      errorMessage.value = '无法连接到RTSP服务器，请检查网络和设备状态';
    }
    
  } catch (error) {
    connectionStatus.value = 'failed';
    errorMessage.value = `连接测试失败: ${error.message}`;
  } finally {
    loading.value = false;
    loadingText.value = '';
  }
};

// 重试连接
const retryConnection = () => {
  testConnection();
};

// 初始化WebRTC-Streamer
const initWebRTCStreamer = () => {
  try {
    if (typeof (window as any).WebRtcStreamer !== 'undefined') {
      // WebRTC-Streamer服务器地址，默认端口8000
      const serverUrl = location.protocol + '//************:8000';
      webRtcServer = new (window as any).WebRtcStreamer('rtspVideo', serverUrl);
      console.log('WebRTC-Streamer初始化成功');
      return true;
    } else {
      console.error('WebRtcStreamer未加载');
      errorMessage.value = 'WebRTC-Streamer库未加载，请检查相关脚本文件';
      return false;
    }
  } catch (error) {
    console.error('WebRTC-Streamer初始化失败:', error);
    errorMessage.value = `WebRTC-Streamer初始化失败: ${(error as Error).message}`;
    return false;
  }
};

// 开始播放
const startPlay = async () => {
  if (!rtspUrl.value) {
    errorMessage.value = '请输入RTSP地址';
    return;
  }

  loading.value = true;
  loadingText.value = '正在初始化播放器...';
  errorMessage.value = '';
  successMessage.value = '';

  try {
    // 初始化WebRTC-Streamer
    if (!webRtcServer) {
      const initialized = initWebRTCStreamer();
      if (!initialized) {
        return;
      }
    }

    loadingText.value = '正在连接RTSP流...';

    // 连接RTSP流
    await new Promise((resolve, reject) => {
      const connectTimeout = setTimeout(() => {
        reject(new Error('连接超时'));
      }, 10000);

      // 监听视频加载事件
      if (videoElement.value) {
        const onLoadedData = () => {
          clearTimeout(connectTimeout);
          videoElement.value?.removeEventListener('loadeddata', onLoadedData);
          videoElement.value?.removeEventListener('error', onError);
          resolve(true);
        };

        const onError = (event: any) => {
          clearTimeout(connectTimeout);
          videoElement.value?.removeEventListener('loadeddata', onLoadedData);
          videoElement.value?.removeEventListener('error', onError);
          reject(new Error('视频加载失败'));
        };

        videoElement.value.addEventListener('loadeddata', onLoadedData);
        videoElement.value.addEventListener('error', onError);
      }

      // 连接RTSP流
      webRtcServer.connect(rtspUrl.value);
    });

    // 显示视频元素
    if (videoElement.value) {
      videoElement.value.style.display = 'block';
    }

    isPlaying.value = true;
    connectionStatus.value = 'connected';
    successMessage.value = 'RTSP视频流播放成功';

  } catch (error) {
    console.error('播放失败:', error);
    errorMessage.value = `播放失败: ${error.message}`;
    connectionStatus.value = 'failed';
  } finally {
    loading.value = false;
    loadingText.value = '';
  }
};

// 停止播放
const stopPlay = () => {
  try {
    if (webRtcServer) {
      webRtcServer.disconnect();
    }

    if (videoElement.value) {
      videoElement.value.style.display = 'none';
      videoElement.value.srcObject = null;
    }

    isPlaying.value = false;
    connectionStatus.value = 'disconnected';
    successMessage.value = '视频播放已停止';

  } catch (error) {
    console.error('停止播放失败:', error);
    errorMessage.value = `停止播放失败: ${error.message}`;
  }
};

// 重置测试
const resetTest = () => {
  // 先停止播放
  if (isPlaying.value) {
    stopPlay();
  }

  connectionStatus.value = 'disconnected';
  errorMessage.value = '';
  successMessage.value = '';
  parsedInfo.value = null;
  loading.value = false;
};

// 显示模态框
const showModal = (videoUrl?: string) => {
  if (videoUrl) {
    rtspUrl.value = videoUrl;
  }
  open.value = true;
  resetTest();
};

// 关闭模态框
const handleCancel = () => {
  // 停止播放
  if (isPlaying.value) {
    stopPlay();
  }

  open.value = false;
  resetTest();
};

// 组件销毁时清理
const cleanup = () => {
  if (webRtcServer) {
    try {
      webRtcServer.disconnect();
    } catch (error) {
      console.error('清理WebRTC连接失败:', error);
    }
    webRtcServer = null;
  }
};

// 添加全局类型声明
declare global {
  interface Window {
    WebRtcStreamer: any;
  }
}

defineExpose({
  showModal,
  cleanup
});
</script>

<style scoped>
.rtsp-test-modal :deep(.ant-modal-content) {
  border-radius: 16px;
}

.rtsp-test-modal :deep(.ant-modal-body) {
  padding: 0;
}
</style>
