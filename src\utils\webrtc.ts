/**
 * WebRTC工具类
 * 用于处理RTSP视频流的WebRTC播放
 */

export interface WebRTCConfig {
  iceServers: RTCIceServer[];
  signalServerUrl: string;
}

export interface VideoStats {
  videoCodec?: string;
  videoResolution?: string;
  frameRate?: number;
  bitrate?: number;
  packetsReceived?: number;
  packetsLost?: number;
}

export class WebRTCPlayer {
  private peerConnection: RTCPeerConnection | null = null;
  private websocket: WebSocket | null = null;
  private videoElement: HTMLVideoElement | null = null;
  private statsInterval: number | null = null;
  
  private config: WebRTCConfig = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' }
    ],
    signalServerUrl: 'ws://localhost:8080/webrtc/signal'
  };
  
  // 事件回调
  public onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
  public onTrack?: (stream: MediaStream) => void;
  public onError?: (error: Error) => void;
  public onStats?: (stats: VideoStats) => void;
  
  constructor(config?: Partial<WebRTCConfig>) {
    if (config) {
      this.config = { ...this.config, ...config };
    }
  }
  
  /**
   * 初始化WebRTC连接
   */
  async init(videoElement: HTMLVideoElement): Promise<void> {
    this.videoElement = videoElement;
    
    try {
      // 创建RTCPeerConnection
      this.peerConnection = new RTCPeerConnection({
        iceServers: this.config.iceServers
      });
      
      // 设置事件监听
      this.setupEventListeners();
      
      console.log('WebRTC初始化完成');
    } catch (error) {
      console.error('WebRTC初始化失败:', error);
      throw new Error(`WebRTC初始化失败: ${error.message}`);
    }
  }
  
  /**
   * 开始播放RTSP视频流
   */
  async play(rtspUrl: string): Promise<void> {
    if (!this.peerConnection) {
      throw new Error('WebRTC未初始化');
    }
    
    try {
      // 连接WebSocket信令服务器
      await this.connectWebSocket();
      
      // 发送播放请求
      this.sendMessage({
        type: 'play',
        rtspUrl: rtspUrl
      });
      
      console.log('开始播放RTSP视频流:', rtspUrl);
    } catch (error) {
      console.error('播放失败:', error);
      throw new Error(`播放失败: ${error.message}`);
    }
  }
  
  /**
   * 停止播放
   */
  stop(): void {
    try {
      // 停止统计收集
      if (this.statsInterval) {
        clearInterval(this.statsInterval);
        this.statsInterval = null;
      }
      
      // 关闭WebRTC连接
      if (this.peerConnection) {
        this.peerConnection.close();
        this.peerConnection = null;
      }
      
      // 关闭WebSocket连接
      if (this.websocket) {
        this.websocket.close();
        this.websocket = null;
      }
      
      // 清理视频元素
      if (this.videoElement) {
        this.videoElement.srcObject = null;
      }
      
      console.log('WebRTC播放已停止');
    } catch (error) {
      console.error('停止播放失败:', error);
    }
  }
  
  /**
   * 截图
   */
  captureFrame(): string | null {
    if (!this.videoElement) {
      return null;
    }
    
    try {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      canvas.width = this.videoElement.videoWidth;
      canvas.height = this.videoElement.videoHeight;
      
      if (ctx) {
        ctx.drawImage(this.videoElement, 0, 0);
        return canvas.toDataURL('image/png');
      }
      
      return null;
    } catch (error) {
      console.error('截图失败:', error);
      return null;
    }
  }
  
  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.peerConnection) return;
    
    // 连接状态变化
    this.peerConnection.onconnectionstatechange = () => {
      if (this.peerConnection && this.onConnectionStateChange) {
        this.onConnectionStateChange(this.peerConnection.connectionState);
      }
    };
    
    // ICE连接状态变化
    this.peerConnection.oniceconnectionstatechange = () => {
      if (this.peerConnection) {
        console.log('ICE连接状态:', this.peerConnection.iceConnectionState);
      }
    };
    
    // 接收远程流
    this.peerConnection.ontrack = (event) => {
      console.log('收到远程视频流:', event);
      
      if (this.videoElement && event.streams[0]) {
        this.videoElement.srcObject = event.streams[0];
        
        if (this.onTrack) {
          this.onTrack(event.streams[0]);
        }
        
        // 开始统计收集
        this.startStatsCollection();
      }
    };
    
    // ICE候选
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendMessage({
          type: 'ice-candidate',
          candidate: event.candidate
        });
      }
    };
  }
  
  /**
   * 连接WebSocket信令服务器
   */
  private connectWebSocket(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket(this.config.signalServerUrl);
        
        this.websocket.onopen = () => {
          console.log('WebSocket连接成功');
          resolve();
        };
        
        this.websocket.onmessage = async (event) => {
          try {
            const message = JSON.parse(event.data);
            await this.handleSignalingMessage(message);
          } catch (error) {
            console.error('处理信令消息失败:', error);
          }
        };
        
        this.websocket.onerror = (error) => {
          console.error('WebSocket错误:', error);
          if (this.onError) {
            this.onError(new Error('WebSocket连接失败'));
          }
          reject(new Error('WebSocket连接失败'));
        };
        
        this.websocket.onclose = () => {
          console.log('WebSocket连接关闭');
        };
        
      } catch (error) {
        reject(error);
      }
    });
  }
  
  /**
   * 处理信令消息
   */
  private async handleSignalingMessage(message: any): Promise<void> {
    if (!this.peerConnection) return;
    
    try {
      switch (message.type) {
        case 'offer':
          await this.peerConnection.setRemoteDescription(new RTCSessionDescription(message.offer));
          const answer = await this.peerConnection.createAnswer();
          await this.peerConnection.setLocalDescription(answer);
          
          this.sendMessage({
            type: 'answer',
            answer: answer
          });
          break;
          
        case 'ice-candidate':
          await this.peerConnection.addIceCandidate(new RTCIceCandidate(message.candidate));
          break;
          
        case 'error':
          if (this.onError) {
            this.onError(new Error(message.message));
          }
          break;
          
        default:
          console.log('未知信令消息:', message);
      }
    } catch (error) {
      console.error('处理信令消息失败:', error);
      if (this.onError) {
        this.onError(error);
      }
    }
  }
  
  /**
   * 发送信令消息
   */
  private sendMessage(message: any): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    }
  }
  
  /**
   * 开始统计信息收集
   */
  private startStatsCollection(): void {
    if (!this.peerConnection) return;
    
    this.statsInterval = setInterval(async () => {
      try {
        if (this.peerConnection) {
          const statsReport = await this.peerConnection.getStats();
          const stats = this.parseStats(statsReport);
          
          if (this.onStats) {
            this.onStats(stats);
          }
        }
      } catch (error) {
        console.error('获取统计信息失败:', error);
      }
    }, 1000);
  }
  
  /**
   * 解析统计信息
   */
  private parseStats(statsReport: RTCStatsReport): VideoStats {
    const stats: VideoStats = {};
    
    statsReport.forEach((report) => {
      if (report.type === 'inbound-rtp' && report.mediaType === 'video') {
        stats.frameRate = report.framesPerSecond;
        stats.bitrate = Math.round(report.bytesReceived * 8 / 1000); // kbps
        stats.packetsReceived = report.packetsReceived;
        stats.packetsLost = report.packetsLost;
      }
      
      if (report.type === 'track' && report.kind === 'video') {
        stats.videoResolution = `${report.frameWidth}x${report.frameHeight}`;
      }
      
      if (report.type === 'codec' && report.mimeType?.includes('video')) {
        stats.videoCodec = report.mimeType.split('/')[1];
      }
    });
    
    return stats;
  }
}

/**
 * 创建WebRTC播放器实例
 */
export function createWebRTCPlayer(config?: Partial<WebRTCConfig>): WebRTCPlayer {
  return new WebRTCPlayer(config);
}

/**
 * 检查WebRTC支持
 */
export function checkWebRTCSupport(): boolean {
  return !!(window.RTCPeerConnection && window.WebSocket);
}

/**
 * 获取用户媒体设备权限
 */
export async function getUserMedia(constraints: MediaStreamConstraints): Promise<MediaStream> {
  try {
    return await navigator.mediaDevices.getUserMedia(constraints);
  } catch (error) {
    console.error('获取用户媒体失败:', error);
    throw new Error(`获取用户媒体失败: ${error.message}`);
  }
}
