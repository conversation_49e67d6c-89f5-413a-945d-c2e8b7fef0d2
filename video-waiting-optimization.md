# 视频等待数据问题优化

## 问题描述
用户点击播放视频时，控制台打印出"视频等待数据..."，然后视频没有播放，卡在等待状态。

## 问题分析

### 可能的原因
1. **网络延迟**: HLS流片段加载缓慢
2. **缓冲不足**: 视频缓冲区数据不足以开始播放
3. **流未就绪**: 后端视频流转换尚未完成
4. **HLS配置问题**: HLS.js配置可能过于保守
5. **错误恢复不足**: 等待状态下缺乏有效的恢复机制

## 优化方案

### 1. 增强onVideoWaiting函数
```javascript
function onVideoWaiting() {
  console.log('视频等待数据...', {
    isPlaying: isPlaying.value,
    starting: starting.value,
    hasHls: !!hls
  });
  
  // 如果HLS实例存在，尝试重新加载
  if (hls) {
    console.log('尝试重新加载HLS流以解决等待问题');
    try {
      hls.startLoad();
    } catch (error) {
      console.error('重新加载HLS流失败:', error);
    }
  }
  
  // 设置超时恢复机制
  waitingTimeout = setTimeout(() => {
    if (videoElement.value && videoElement.value.readyState < 3) {
      console.warn('视频长时间等待数据，尝试恢复播放');
      
      if (hls) {
        try {
          hls.recoverMediaError();
        } catch (error) {
          attemptAutoReconnect();
        }
      } else if (videoElement.value) {
        const video = videoElement.value;
        video.load();
        video.play().catch(error => {
          console.error('重新播放失败:', error);
        });
      }
    }
  }, 5000); // 5秒后尝试恢复
}
```

### 2. 优化onVideoCanPlay函数
```javascript
function onVideoCanPlay() {
  console.log('视频可以播放', {
    isPlaying: isPlaying.value,
    starting: starting.value,
    paused: videoElement.value?.paused,
    readyState: videoElement.value?.readyState
  });
  
  // 清除等待超时
  clearWaitingTimeout();
  
  // 更积极地开始播放
  if (starting.value && videoElement.value && !videoElement.value.paused) {
    isPlaying.value = true;
    starting.value = false;
    startDurationTimer();
  }
  
  // 如果视频可以播放但未播放，尝试播放
  if (!isPlaying.value && starting.value && videoElement.value) {
    videoElement.value.play().then(() => {
      isPlaying.value = true;
      starting.value = false;
      startDurationTimer();
    }).catch(error => {
      console.error('播放视频失败:', error);
    });
  }
}
```

### 3. 增强onVideoStalled函数
```javascript
function onVideoStalled() {
  console.log('视频播放停滞', {
    isPlaying: isPlaying.value,
    currentTime: videoElement.value?.currentTime,
    buffered: videoElement.value?.buffered.length || 0,
    networkState: videoElement.value?.networkState,
    readyState: videoElement.value?.readyState
  });
  
  // 立即尝试重新加载
  if (hls) {
    try {
      hls.startLoad();
    } catch (error) {
      console.error('重新加载HLS流失败:', error);
    }
  }
  
  // 延迟恢复机制
  setTimeout(() => {
    if (videoElement.value && videoElement.value.readyState < 3) {
      if (hls) {
        try {
          hls.recoverMediaError();
        } catch (error) {
          attemptAutoReconnect();
        }
      }
    }
  }, 3000);
}
```

### 4. 添加等待超时清理
```javascript
function clearWaitingTimeout() {
  if (waitingTimeout) {
    clearTimeout(waitingTimeout);
    waitingTimeout = null;
  }
}
```

## 关键改进点

### 1. 主动恢复机制
- **立即重试**: 在等待和停滞事件中立即尝试重新加载
- **超时恢复**: 设置超时机制，长时间等待后自动尝试恢复
- **多层恢复**: 从简单的重新加载到媒体错误恢复，再到完全重连

### 2. 更好的状态管理
- **详细日志**: 记录视频状态信息，便于调试
- **状态同步**: 确保播放状态与实际视频状态同步
- **超时清理**: 防止多个超时定时器同时运行

### 3. 积极的播放策略
- **canplay事件优化**: 视频可播放时立即尝试播放
- **状态检查**: 检查各种播放条件，确保不遗漏播放机会

## 预期效果

### 1. 减少等待时间
- 视频等待数据时能够更快恢复
- 减少用户等待时间

### 2. 提高播放成功率
- 多层恢复机制提高播放成功率
- 自动处理常见的播放问题

### 3. 更好的用户体验
- 减少播放失败的情况
- 提供更稳定的视频播放体验

## 测试建议

### 1. 网络环境测试
- 在不同网络条件下测试播放
- 模拟网络延迟和不稳定情况

### 2. 并发测试
- 多个视频同时播放
- 频繁开关视频播放

### 3. 长时间测试
- 长时间播放测试
- 检查内存泄漏和性能问题

## 注意事项

1. **避免过度重试**: 设置合理的重试间隔和次数限制
2. **资源清理**: 确保所有超时定时器都能正确清理
3. **错误处理**: 完善的错误处理机制，避免无限循环重试
4. **性能考虑**: 避免过于频繁的恢复操作影响性能
